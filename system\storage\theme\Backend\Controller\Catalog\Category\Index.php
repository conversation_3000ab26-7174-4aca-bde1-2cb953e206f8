<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Index extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява подготовката на данните за списъка с категории
     */
    public function execute() {
        $this->setTitle('Категории');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/category');
    }

    /**
     * Подготвя всички данни за страницата с категории
     */
    public function prepareData() {
        $this->prepareCategoryListData()
             ->prepareFilterOptions()
             ->prepareCategoryItems()
             ->preparePagination();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/category')
        ]);
    }

    /**
     * Подготвя основните данни за списъка с категории
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCategoryListData() {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/category' => 'categories',
            'tool/image' => 'image'
        ]);

        // URL адреси - използваме getAdminLinks за групово извличане
        $routes = [
            'add_new_url' => 'catalog/category/add',
            'delete_url' => 'catalog/category/delete&category_id=CATEGORY_ID',
            'edit_url' => 'catalog/category/edit&category_id=CATEGORY_ID',
            'copy_url' => 'catalog/category/copy&category_id=CATEGORY_ID'
        ];

        // Добавяне на URL адресите към данните
        $this->setData($this->getAdminLinks($routes));

        return $this;
    }

    /**
     * Подготвя опциите за филтриране и сортиране
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareFilterOptions() {
        // Получаване на данните за филтрите
        $filter_data = $this->getFilterData();

        // Опции за сортиране
        $sort_options = [
            ['value' => 'cd.name-ASC', 'text' => 'Име (А-Я)'],
            ['value' => 'cd.name-DESC', 'text' => 'Име (Я-А)'],
            ['value' => 'c.sort_order-ASC', 'text' => 'Позиция (възх.)'],
            ['value' => 'c.sort_order-DESC', 'text' => 'Позиция (низх.)'],
            ['value' => 'c.date_added-DESC', 'text' => 'Последно добавени'],
            ['value' => 'c.date_added-ASC', 'text' => 'Първо добавени']
        ];

        // URL адреси за филтри
        $filter_urls = $this->getAdminLinks([
            'sort_url' => 'catalog/category',
            'limit_url' => 'catalog/category',
            'filter_active_url' => 'catalog/category',
            'filter_parent_url' => 'catalog/category'
        ], [
            'sort_url' => '&sort=SORT_VALUE',
            'limit_url' => '&limit=LIMIT_VALUE',
            'filter_active_url' => '&filter_status=FILTER_VALUE',
            'filter_parent_url' => '&filter_parent_id=FILTER_VALUE'
        ]);

        // Текущи филтри
        $current_filters = [
            'filter_active' => isset($filter_data['filter_status']) && $filter_data['filter_status'] == 1,
            'filter_parent' => isset($filter_data['filter_parent_id']) ? $filter_data['filter_parent_id'] : '',
            'view_type' => $this->requestGet('view') ?: 'list',
            'limit' => $filter_data['limit']
        ];

        // Добавяне на данните към $this->data
        $this->setData([
            'sort_options' => $sort_options,
            'filter_data' => $filter_data
        ])
        ->setData($filter_urls)
        ->setData($current_filters);

        return $this;
    }

    /**
     * Подготвя списъка с категории в йерархичен вид
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCategoryItems() {
        // Получаване на данните за филтрите
        $filter_data = $this->data['filter_data'];

        // Получаване на категориите
        $results = $this->categories->getCategories($filter_data);

        // Подготовка на данните за категориите
        $categories = [];

        foreach ($results as $result) {
            // Броене на продуктите в категорията
            $product_count = $this->getProductCountForCategory($result['category_id']);

            // Подготовка на данните за категорията
            $categories[] = [
                'category_id' => $result['category_id'],
                'name' => $result['name'],
                'parent_id' => $result['parent_id'],
                'sort_order' => $result['sort_order'],
                'status' => $result['status'],
                'status_text' => $result['status'] ? 'Активна' : 'Неактивна',
                'status_class' => $result['status'] ? 'status-active' : 'status-inactive',
                'product_count' => $product_count,
                'level' => $this->getCategoryLevel($result['category_id']),
                'edit' => str_replace('CATEGORY_ID', $result['category_id'], $this->data['edit_url']),
                'delete' => str_replace('CATEGORY_ID', $result['category_id'], $this->data['delete_url']),
                'products_url' => $this->getAdminLink('catalog/product', 'filter_category_id=' . $result['category_id'])
            ];
        }

        // Сортиране на категориите в йерархичен ред
        $categories = $this->buildCategoryHierarchy($categories);

        F()->log->developer($categories, __FILE__, __LINE__);

        // Добавяне на категориите към данните
        $this->setData('categories', $categories);

        return $this;
    }

    /**
     * Подготвя пагинацията
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePagination() {
        // Получаване на данните за филтрите
        $filter_data = $this->data['filter_data'];

        // Получаване на общия брой категории
        $category_total = $this->categories->getTotalCategories($filter_data);

        // Опции за брой категории на страница
        $limits = [
            ['value' => 10, 'text' => '10 на страница'],
            ['value' => 20, 'text' => '20 на страница'],
            ['value' => 50, 'text' => '50 на страница'],
            ['value' => 100, 'text' => '100 на страница']
        ];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $category_total;
        $pagination->page = $filter_data['page'];
        $pagination->limit = $filter_data['limit'];
        $pagination->url = $this->getAdminLink('catalog/category', '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('catalog/category', '&limit={limit}'));
        $pagination->setProductText('категории');

        // Генериране на HTML код за цялата пагинация
        $this->setData('pagination_html', $pagination->render());

        return $this;
    }

    /**
     * Получава данните за филтрите от заявката
     *
     * @return array Масив с данни за филтрите
     */
    private function getFilterData() {
        $filter_data = [];

        // Сортиране
        $sort = $this->requestGet('sort') ?: 'cd.name';
        $order = $this->requestGet('order') ?: 'ASC';

        // Ако сортирането е във формат 'field-ORDER'
        if (strpos($sort, '-') !== false) {
            list($sort, $order) = explode('-', $sort);
        }

        $filter_data['sort'] = $sort;
        $filter_data['order'] = $order;

        // Пагинация
        $page = (int)$this->requestGet('page') ?: 1;
        $limit = (int)$this->requestGet('limit') ? $this->requestGet('limit') : 20;

        $filter_data['start'] = ($page - 1) * $limit;
        $filter_data['limit'] = $limit;
        $filter_data['page'] = $page;

        // Филтри
        $filter_fields = [
            'filter_name',
            'filter_status',
            'filter_parent_id'
        ];

        foreach ($filter_fields as $field) {
            if ($value = $this->requestGet($field)) {
                $filter_data[$field] = $value;
            }
        }

        return $filter_data;
    }

    /**
     * Получава броя продукти в дадена категория
     *
     * @param int $category_id ID на категорията
     * @return int Брой продукти
     */
    private function getProductCountForCategory($category_id) {
        // Зареждане на модела за продукти, ако не е зареден
        if (!isset($this->products)) {
            $this->loadModelAs('catalog/product', 'products');
        }

        return $this->products->getTotalProducts(['filter_category_id' => $category_id]);
    }

    /**
     * Получава нивото на категорията в йерархията
     *
     * @param int $category_id ID на категорията
     * @return int Ниво на категорията
     */
    private function getCategoryLevel($category_id) {
        $path = $this->categories->getCategoryPath($category_id);
        return count($path) - 1; // Минус 1, защото включва и самата категория
    }

    /**
     * Изгражда йерархичен списък от категории
     *
     * @param array $categories Плосък списък с категории
     * @return array Йерархичен списък с категории
     */
    private function buildCategoryHierarchy($categories) {
        // За простота, връщаме категориите сортирани по parent_id и sort_order
        usort($categories, function($a, $b) {
            if ($a['parent_id'] == $b['parent_id']) {
                return $a['sort_order'] - $b['sort_order'];
            }
            return $a['parent_id'] - $b['parent_id'];
        });

        return $categories;
    }
}
