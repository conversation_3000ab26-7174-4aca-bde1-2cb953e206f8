<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Save extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Запазване на категория
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();

        // Валидация на задължителни полета
        if (!isset($post['category_description']) || !is_array($post['category_description'])) {
            $json['error'] = 'Липсва описание на категорията';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка дали има име поне за един език
        $has_name = false;
        foreach ($post['category_description'] as $language_id => $description) {
            if (!empty($description['name'])) {
                $has_name = true;
                break;
            }
        }

        if (!$has_name) {
            $json['error'] = 'Моля въведете име на категорията поне за един език';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'tool/image' => 'imageModel'
        ]);

        // Подготвяне на данните за категорията
        $data = [
            'parent_id' => $post['parent_id'] ?? 0,
            'top' => $post['top'] ?? 0,
            'column' => $post['column'] ?? 1,
            'sort_order' => $post['sort_order'] ?? 0,
            'status' => $post['status'] ?? 0,
            'category_description' => $post['category_description'],
            'category_store' => $post['category_store'] ?? [0],
            'category_seo_url' => $post['category_seo_url'] ?? [],
            'image' => ''
        ];

        // Обработка на изображението
        if (!empty($post['image'])) {
            $image_path = $post['image'];
            $image_file = ThemeData()->getImageServerPath() . $image_path;

            if (file_exists($image_file)) {
                $data['image'] = $image_path;
            }
        }

        // Валидация на родителската категория
        if ($data['parent_id'] > 0) {
            $parent_category = $this->categoryModel->getCategory($data['parent_id']);
            if (!$parent_category) {
                $json['error'] = 'Избраната родителска категория не съществува';
                $this->setJSONResponseOutput($json);
                return;
            }
        }

        // Запазване на категорията
        $category_id = $post['category_id'] ?? 0;

        try {
            if ($category_id) {
                // Проверка дали категорията не се опитва да стане родител на себе си
                if ($data['parent_id'] == $category_id) {
                    $json['error'] = 'Категорията не може да бъде родител на себе си';
                    $this->setJSONResponseOutput($json);
                    return;
                }

                // Проверка за циклична зависимост
                if ($this->wouldCreateCycle($category_id, $data['parent_id'])) {
                    $json['error'] = 'Избраната родителска категория би създала циклична зависимост';
                    $this->setJSONResponseOutput($json);
                    return;
                }

                $this->categoryModel->editCategory($category_id, $data);
                $json['success'] = 'Категорията беше успешно актуализирана';
            } else {
                $category_id = $this->categoryModel->addCategory($data);
                $json['success'] = 'Категорията беше успешно добавена';
            }

            // Добавяне на ID на категорията в отговора
            $json['category_id'] = $category_id;

            // Добавяне на URL за пренасочване
            $json['redirect'] = $this->getAdminLink('catalog/category/edit', 'category_id=' . $category_id, true);

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване на категорията: ' . $e->getMessage();
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали промяната на родителската категория би създала циклична зависимост
     *
     * @param int $category_id ID на категорията
     * @param int $parent_id ID на новата родителска категория
     * @return bool True ако би създала цикъл
     */
    private function wouldCreateCycle($category_id, $parent_id) {
        if ($parent_id == 0) {
            return false; // Няма родител, няма цикъл
        }

        // Получаване на всички деца на текущата категория
        $children = $this->getAllCategoryChildren($category_id);
        
        // Проверка дали новият родител е сред децата
        return in_array($parent_id, $children);
    }

    /**
     * Получава всички деца на дадена категория (рекурсивно)
     *
     * @param int $category_id ID на категорията
     * @return array Масив с ID-та на всички деца
     */
    private function getAllCategoryChildren($category_id) {
        $children = [];
        
        // Получаване на директните деца
        $direct_children = $this->categoryModel->getCategories(['filter_parent_id' => $category_id]);
        
        foreach ($direct_children as $child) {
            $children[] = $child['category_id'];
            
            // Рекурсивно получаване на децата на детето
            $grandchildren = $this->getAllCategoryChildren($child['category_id']);
            $children = array_merge($children, $grandchildren);
        }
        
        return $children;
    }
}
