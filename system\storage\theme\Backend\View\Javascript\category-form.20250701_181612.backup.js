/**
 * Category Form JavaScript Module
 * Управлява функционалността на формата за категории
 */
class CategoryForm extends BackendModule {
    constructor() {
        super();
        this.init();
    }

    /**
     * Инициализация на модула
     */
    init() {
        this.initFormValidation();
        this.initImageManager();
        this.initRichTextEditors();
        this.initFormSubmission();
        this.initTabNavigation();
        this.bindEvents();
    }

    /**
     * Инициализация на валидацията на формата
     */
    initFormValidation() {
        const form = document.getElementById('form-category');
        if (!form) return;

        // Валидация при изпращане на формата
        form.addEventListener('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
                return false;
            }
        });

        // Валидация в реalno време
        this.initRealTimeValidation();
    }

    /**
     * Инициализация на валидация в реално време
     */
    initRealTimeValidation() {
        // Валидация на имената на категориите
        const nameInputs = document.querySelectorAll('input[name*="[name]"]');
        nameInputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateCategoryName(input);
            });
        });

        // Валидация на SEO URL-та
        const seoInputs = document.querySelectorAll('input[name*="category_seo_url"]');
        seoInputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateSeoUrl(input);
            });
        });

        // Валидация на позицията
        const sortOrderInput = document.getElementById('input-sort-order');
        if (sortOrderInput) {
            sortOrderInput.addEventListener('blur', () => {
                this.validateSortOrder(sortOrderInput);
            });
        }
    }

    /**
     * Валидация на цялата форма
     */
    validateForm() {
        let isValid = true;
        const errors = [];

        // Проверка дали има поне едно име на категория
        const nameInputs = document.querySelectorAll('input[name*="[name]"]');
        let hasName = false;

        nameInputs.forEach(input => {
            if (input.value.trim()) {
                hasName = true;
            }
        });

        if (!hasName) {
            errors.push('Моля въведете име на категорията поне за един език');
            isValid = false;
        }

        // Валидация на SEO URL-та
        const seoInputs = document.querySelectorAll('input[name*="category_seo_url"]');
        seoInputs.forEach(input => {
            if (!this.validateSeoUrl(input)) {
                isValid = false;
            }
        });

        // Показване на грешките
        if (!isValid) {
            this.showValidationErrors(errors);
        }

        return isValid;
    }

    /**
     * Валидация на име на категория
     */
    validateCategoryName(input) {
        const value = input.value.trim();
        const isValid = value.length > 0 && value.length <= 255;

        this.toggleInputValidation(input, isValid, 
            isValid ? '' : 'Името на категорията трябва да бъде между 1 и 255 символа');

        return isValid;
    }

    /**
     * Валидация на SEO URL
     */
    validateSeoUrl(input) {
        const value = input.value.trim();
        
        if (!value) {
            this.toggleInputValidation(input, true, '');
            return true;
        }

        // Проверка за валиден SEO URL формат
        const seoUrlPattern = /^[a-z0-9\-_\/]+$/;
        const isValid = seoUrlPattern.test(value) && value.length <= 255;

        this.toggleInputValidation(input, isValid, 
            isValid ? '' : 'SEO URL може да съдържа само малки букви, цифри, тирета и долни черти');

        return isValid;
    }

    /**
     * Валидация на позиция
     */
    validateSortOrder(input) {
        const value = input.value.trim();
        
        if (!value) {
            this.toggleInputValidation(input, true, '');
            return true;
        }

        const numValue = parseInt(value);
        const isValid = !isNaN(numValue) && numValue >= 0;

        this.toggleInputValidation(input, isValid, 
            isValid ? '' : 'Позицията трябва да бъде положително число');

        return isValid;
    }

    /**
     * Превключва визуалната валидация на поле
     */
    toggleInputValidation(input, isValid, errorMessage) {
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            if (feedback) feedback.textContent = errorMessage;
        }
    }

    /**
     * Показва грешки от валидацията
     */
    showValidationErrors(errors) {
        if (errors.length === 0) return;

        const errorHtml = errors.map(error => `<li>${error}</li>`).join('');
        
        this.showAlert('danger', `
            <strong>Грешки във формата:</strong>
            <ul class="mb-0 mt-2">${errorHtml}</ul>
        `);
    }

    /**
     * Инициализация на мениджъра за изображения
     */
    initImageManager() {
        // Бутон за редактиране на изображение
        const editImageBtn = document.querySelector('[data-oc-toggle="image"]');
        if (editImageBtn) {
            editImageBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.openImageManager(editImageBtn);
            });
        }

        // Бутон за изчистване на изображение
        const clearImageBtn = document.querySelector('[data-oc-toggle="clear"]');
        if (clearImageBtn) {
            clearImageBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearImage(clearImageBtn);
            });
        }
    }

    /**
     * Отваря мениджъра за изображения
     */
    openImageManager(button) {
        const target = button.getAttribute('data-oc-target');
        const thumb = button.getAttribute('data-oc-thumb');
        
        if (window.ImageManager) {
            window.ImageManager.open({
                mode: 'single',
                onSelect: (images) => {
                    if (images.length > 0) {
                        const image = images[0];
                        this.setImage(target, thumb, image.path);
                    }
                }
            });
        } else {
            console.warn('ImageManager не е зареден');
        }
    }

    /**
     * Задава изображение
     */
    setImage(targetSelector, thumbSelector, imagePath) {
        const targetInput = document.querySelector(targetSelector);
        const thumbImg = document.querySelector(thumbSelector);
        
        if (targetInput) {
            targetInput.value = imagePath;
        }
        
        if (thumbImg) {
            thumbImg.src = imagePath;
        }
    }

    /**
     * Изчиства изображението
     */
    clearImage(button) {
        const target = button.getAttribute('data-oc-target');
        const thumb = button.getAttribute('data-oc-thumb');
        
        const targetInput = document.querySelector(target);
        const thumbImg = document.querySelector(thumb);
        
        if (targetInput) {
            targetInput.value = '';
        }
        
        if (thumbImg) {
            const placeholder = thumbImg.getAttribute('data-oc-placeholder');
            thumbImg.src = placeholder || '';
        }
    }

    /**
     * Инициализация на rich text редакторите
     */
    initRichTextEditors() {
        const editorContainers = document.querySelectorAll('.editor-container');
        
        editorContainers.forEach(container => {
            const textarea = container.querySelector('textarea');
            if (textarea && window.AdvancedRichTextEditor) {
                new AdvancedRichTextEditor(textarea);
            }
        });
    }

    /**
     * Инициализация на изпращането на формата
     */
    initFormSubmission() {
        const form = document.getElementById('form-category');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitForm();
        });
    }

    /**
     * Изпраща формата чрез AJAX
     */
    submitForm() {
        const form = document.getElementById('form-category');
        if (!form) return;

        // Показване на loading индикатор
        this.showLoadingState(true);

        const formData = new FormData(form);

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.handleFormResponse(data);
        })
        .catch(error => {
            console.error('Грешка при изпращане на формата:', error);
            this.showAlert('danger', 'Възникна грешка при запазване на категорията');
        })
        .finally(() => {
            this.showLoadingState(false);
        });
    }

    /**
     * Обработва отговора от сървъра
     */
    handleFormResponse(data) {
        if (data.error) {
            this.showAlert('danger', data.error);
        } else if (data.success) {
            this.showAlert('success', data.success);
            
            // Пренасочване, ако е указано
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1500);
            }
        }
    }

    /**
     * Показва/скрива loading състояние
     */
    showLoadingState(show) {
        const submitBtn = document.querySelector('button[type="submit"][form="form-category"]');
        
        if (submitBtn) {
            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Запазване...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fa-solid fa-floppy-disk"></i>';
            }
        }
    }

    /**
     * Инициализация на навигацията между табовете
     */
    initTabNavigation() {
        // Запомняне на активния таб
        const tabs = document.querySelectorAll('[data-bs-toggle="tab"], [data-bs-toggle="pill"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const tabId = e.target.getAttribute('href');
                localStorage.setItem('category-form-active-tab', tabId);
            });
        });

        // Възстановяване на активния таб
        const activeTab = localStorage.getItem('category-form-active-tab');
        if (activeTab) {
            const tabElement = document.querySelector(`[href="${activeTab}"]`);
            if (tabElement) {
                const tab = new bootstrap.Tab(tabElement);
                tab.show();
            }
        }
    }

    /**
     * Свързва събитията
     */
    bindEvents() {
        // Автоматично генериране на SEO URL от името
        const nameInputs = document.querySelectorAll('input[name*="[name]"]');
        nameInputs.forEach(input => {
            input.addEventListener('input', () => {
                this.autoGenerateSeoUrl(input);
            });
        });
    }

    /**
     * Автоматично генерира SEO URL от името на категорията
     */
    autoGenerateSeoUrl(nameInput) {
        const name = nameInput.name;
        const languageMatch = name.match(/\[(\d+)\]/);
        
        if (!languageMatch) return;
        
        const languageId = languageMatch[1];
        const seoInput = document.querySelector(`input[name*="category_seo_url"][name*="[${languageId}]"]`);
        
        if (seoInput && !seoInput.value.trim()) {
            const seoUrl = this.generateSeoUrl(nameInput.value);
            seoInput.value = seoUrl;
        }
    }

    /**
     * Генерира SEO URL от текст
     */
    generateSeoUrl(text) {
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Премахва специални символи
            .replace(/\s+/g, '-')     // Заменя интервали с тирета
            .replace(/-+/g, '-')      // Премахва множествени тирета
            .trim('-');               // Премахва тирета от началото и края
    }
}

// Инициализация при зареждане на страницата
document.addEventListener('DOMContentLoaded', () => {
    new CategoryForm();
});
