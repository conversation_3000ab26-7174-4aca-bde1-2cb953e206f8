<!-- Categories Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">Категории</h1>
			<p class="text-gray-500 mt-1">Управление на категории и подкатегории на продукти</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ add_new_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-add-line"></i>
				</div>
				<span>Нова категория</span>
			</a>
		</div>
	</div>
</div>
<!-- Filters -->
<div class="bg-white border-b border-gray-200 px-6 py-3">
	<div class="flex flex-wrap items-center gap-4">
		<button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
			<div class="w-5 h-5 flex items-center justify-center mr-2">
				<i class="ri-filter-3-line"></i>
			</div>
			<span>Филтър</span>
		</button>
		<div class="w-full md:w-auto">
			<div class="relative">

			</div>
		</div>
		<div class="w-full md:w-auto">
			<div class="relative">

			</div>
		</div>
		<!-- Filter Modal -->
		<div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
			<div class="bg-white rounded-lg w-full max-w-md mx-4">
				<div class="flex justify-between items-center p-6 border-b border-gray-200">
					<h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
					<button id="close-filter" class="text-gray-400 hover:text-gray-500">
						<div class="w-6 h-6 flex items-center justify-center">
							<i class="ri-close-line"></i>
						</div>
					</button>
				</div>
				<div class="p-6">
					<form id="filter-form" class="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
							<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
								<option value="">Всички</option>
								<option value="active">Активни</option>
								<option value="inactive">Неактивни</option>
							</select>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Тип категория</label>
							<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
								<option value="">Всички</option>
								<option value="main">Основни категории</option>
								<option value="sub">Подкатегории</option>
							</select>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Брой продукти</label>
							<div class="flex space-x-2">
								<input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От">
								<input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До">
							</div>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Дата на създаване</label>
							<div class="flex space-x-2">
								<input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
								<input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
							</div>
						</div>
						<div class="flex justify-end space-x-2 mt-6">
							<button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
							<button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Main Content Area -->
<main
	class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<!-- Categories List -->
	<div class="bg-white rounded shadow overflow-hidden">
		<div
			class="p-6 space-y-4">
			{% if categories %}
				{% for category in categories %}
					<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all{% if category.level > 0 %} ml-{{ category.level * 8 }}{% endif %}">
						{% if category.level > 0 %}
							<div class="category-connector"></div>
						{% endif %}
						<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-drag-move-2-line"></i>
							</div>
						</div>
						<div class="flex-1 flex items-center py-3">
							<div class="flex-1">
								<div class="flex items-center">
									<h3 class="text-base font-medium text-gray-800">{{ category.name }}</h3>
									<span class="status-badge status-{{ category.status ? 'active' : 'inactive' }} ml-3">{{ category.status_text }}</span>
								</div>
								<div class="flex items-center mt-1 text-sm text-gray-500">
									<span>{{ category.product_count }} продукта</span>
									<a href="{{ category.products_url }}" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
								</div>
							</div>
							<div class="flex items-center space-x-2 pr-4">
								<a href="{{ category.edit }}" class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
									<div class="w-5 h-5 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</a>
							</div>
						</div>
					</div>
				{% endfor %}
			{% else %}
				<div class="text-center py-8">
					<div class="text-gray-400 mb-2">
						<i class="ri-folder-open-line text-4xl"></i>
					</div>
					<p class="text-gray-500">Няма намерени категории</p>
				</div>
			{% endif %}
		</div>
		<!-- Pagination -->
		<div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
			<div class="text-sm text-gray-600">
				Показани {{ start }}-{{ end }} от {{ category_total }} категории
			</div>
			<div class="flex items-center space-x-2">
				<div class="relative">
					<button id="per-page-dropdown-btn" class="flex items-center justify-between px-3 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
						<span>{{ limit }} на страница</span>
						<div class="w-5 h-5 flex items-center justify-center ml-2">
							<i class="ri-arrow-down-s-line"></i>
						</div>
					</button>
					<div id="per-page-dropdown" class="hidden absolute right-0 z-10 w-40 mt-1 bg-white border border-gray-200 rounded shadow-lg">
						<ul class="py-1">
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"><a href="{{ url_limit_10 }}">10 на страница</a></li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"><a href="{{ url_limit_20 }}">20 на страница</a></li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"><a href="{{ url_limit_50 }}">50 на страница</a></li>
						</ul>
					</div>
				</div>
				<div class="flex">
					{% if pagination.first %}
						<a href="{{ pagination.first }}" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-left-s-line"></i>
							</div>
						</a>
					{% else %}
						<span class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-400 cursor-not-allowed">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-left-s-line"></i>
							</div>
						</span>
					{% endif %}

					{% for page in pagination.pages %}
						{% if page.current %}
							<span class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-primary text-white">{{ page.text }}</span>
						{% else %}
							<a href="{{ page.href }}" class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">{{ page.text }}</a>
						{% endif %}
					{% endfor %}

					{% if pagination.next %}
						<a href="{{ pagination.next }}" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-right-s-line"></i>
							</div>
						</a>
					{% else %}
						<span class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-400 cursor-not-allowed">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-right-s-line"></i>
							</div>
						</span>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</main>

<!-- CSS стилове за drag & drop функционалност -->
<style>
.drag-ready {
    cursor: grab !important;
}

.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.drop-zone-active {
    background-color: #f0f9ff;
    border-color: #0ea5e9;
}

.drop-zone-hover {
    background-color: #e0f2fe;
}

.drop-indicator {
    position: absolute;
    background-color: #0ea5e9;
    z-index: 1000;
}

.drop-indicator-before {
    top: -2px;
    left: 0;
    right: 0;
    height: 4px;
}

.drop-indicator-after {
    bottom: -2px;
    left: 0;
    right: 0;
    height: 4px;
}

.drop-indicator-inside {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed #0ea5e9;
    background-color: rgba(14, 165, 233, 0.1);
}

.category-connector::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    width: 16px;
    height: 1px;
    background-color: #d1d5db;
}

.category-connector::after {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    width: 1px;
    height: 50%;
    background-color: #d1d5db;
}
</style>

