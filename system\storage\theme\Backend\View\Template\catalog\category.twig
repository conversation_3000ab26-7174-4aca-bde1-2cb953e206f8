<!-- Categories Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">Категории</h1>
			<p class="text-gray-500 mt-1">Управление на категории и подкатегории на продукти</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ add_new_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-add-line"></i>
				</div>
				<span>Нова категория</span>
			</a>
		</div>
	</div>
</div>
<!-- Filters -->
<div class="bg-white border-b border-gray-200 px-6 py-3">
	<div class="flex flex-wrap items-center gap-4">
		<button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
			<div class="w-5 h-5 flex items-center justify-center mr-2">
				<i class="ri-filter-3-line"></i>
			</div>
			<span>Филтър</span>
		</button>
		<div class="w-full md:w-auto">
			<div class="relative">

			</div>
		</div>
		<div class="w-full md:w-auto">
			<div class="relative">

			</div>
		</div>
		<!-- Filter Modal -->
		<div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
			<div class="bg-white rounded-lg w-full max-w-md mx-4">
				<div class="flex justify-between items-center p-6 border-b border-gray-200">
					<h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
					<button id="close-filter" class="text-gray-400 hover:text-gray-500">
						<div class="w-6 h-6 flex items-center justify-center">
							<i class="ri-close-line"></i>
						</div>
					</button>
				</div>
				<div class="p-6">
					<form id="filter-form" class="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
							<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
								<option value="">Всички</option>
								<option value="active">Активни</option>
								<option value="inactive">Неактивни</option>
							</select>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Тип категория</label>
							<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
								<option value="">Всички</option>
								<option value="main">Основни категории</option>
								<option value="sub">Подкатегории</option>
							</select>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Брой продукти</label>
							<div class="flex space-x-2">
								<input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От">
								<input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До">
							</div>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Дата на създаване</label>
							<div class="flex space-x-2">
								<input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
								<input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
							</div>
						</div>
						<div class="flex justify-end space-x-2 mt-6">
							<button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
							<button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Main Content Area -->
<main
	class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<!-- Categories List -->
	<div class="bg-white rounded shadow overflow-hidden">
		<div
			class="p-6 space-y-4">
			{% if categories %}
				{% for category in categories %}
					<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all{% if category.level > 0 %} ml-{{ category.level * 8 }}{% endif %}">
						{% if category.level > 0 %}
							<div class="category-connector"></div>
						{% endif %}
						<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-drag-move-2-line"></i>
							</div>
						</div>
						<div class="flex-1 flex items-center py-3">
							<div class="flex-1">
								<div class="flex items-center">
									<h3 class="text-base font-medium text-gray-800">{{ category.name }}</h3>
									<span class="status-badge status-{{ category.status ? 'active' : 'inactive' }} ml-3">{{ category.status_text }}</span>
								</div>
								<div class="flex items-center mt-1 text-sm text-gray-500">
									<span>{{ category.product_count }} продукта</span>
									<a href="{{ category.products_url }}" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
								</div>
							</div>
							<div class="flex items-center space-x-2 pr-4">
								<a href="{{ category.edit }}" class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
									<div class="w-5 h-5 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</a>
							</div>
						</div>
					</div>
				{% endfor %}
			{% else %}
				<div class="text-center py-8">
					<div class="text-gray-400 mb-2">
						<i class="ri-folder-open-line text-4xl"></i>
					</div>
					<p class="text-gray-500">Няма намерени категории</p>
				</div>
			{% endif %}
		</div>
		<!-- Pagination -->
		<div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
			<div class="text-sm text-gray-600">
				Показани {{ start }}-{{ end }} от {{ category_total }} категории
			</div>
			<div class="flex items-center space-x-2">
				<div class="relative">
					<button id="per-page-dropdown-btn" class="flex items-center justify-between px-3 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
						<span>{{ limit }} на страница</span>
						<div class="w-5 h-5 flex items-center justify-center ml-2">
							<i class="ri-arrow-down-s-line"></i>
						</div>
					</button>
					<div id="per-page-dropdown" class="hidden absolute right-0 z-10 w-40 mt-1 bg-white border border-gray-200 rounded shadow-lg">
						<ul class="py-1">
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"><a href="{{ url_limit_10 }}">10 на страница</a></li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"><a href="{{ url_limit_20 }}">20 на страница</a></li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"><a href="{{ url_limit_50 }}">50 на страница</a></li>
						</ul>
					</div>
				</div>
				<div class="flex">
					{% if pagination.first %}
						<a href="{{ pagination.first }}" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-left-s-line"></i>
							</div>
						</a>
					{% else %}
						<span class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-400 cursor-not-allowed">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-left-s-line"></i>
							</div>
						</span>
					{% endif %}

					{% for page in pagination.pages %}
						{% if page.current %}
							<span class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-primary text-white">{{ page.text }}</span>
						{% else %}
							<a href="{{ page.href }}" class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">{{ page.text }}</a>
						{% endif %}
					{% endfor %}

					{% if pagination.next %}
						<a href="{{ pagination.next }}" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-right-s-line"></i>
							</div>
						</a>
					{% else %}
						<span class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-400 cursor-not-allowed">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-arrow-right-s-line"></i>
							</div>
						</span>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</main>


			<!-- Subcategory 2.2 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all ml-8">
				<div class="category-connector"></div>
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Дамски</h3>
							<span class="status-badge status-active ml-3">Активна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>22 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
			<!-- Category 3 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all">
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Дом и градина</h3>
							<span class="status-badge status-active ml-3">Активна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>37 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
			<!-- Subcategory 3.1 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all ml-8">
				<div class="category-connector"></div>
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Мебели</h3>
							<span class="status-badge status-active ml-3">Активна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>15 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
			<!-- Subcategory 3.2 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all ml-8">
				<div class="category-connector"></div>
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Градински инструменти</h3>
							<span class="status-badge status-active ml-3">Активна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>12 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
			<!-- Subcategory 3.3 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all ml-8">
				<div class="category-connector"></div>
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Декорация</h3>
							<span class="status-badge status-active ml-3">Активна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>10 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
			<!-- Category 4 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all">
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Спорт</h3>
							<span class="status-badge status-active ml-3">Активна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>24 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
			<!-- Category 5 -->
			<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all">
				<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400">
					<div class="w-5 h-5 flex items-center justify-center">
						<i class="ri-drag-move-2-line"></i>
					</div>
				</div>
				<div class="flex-1 flex items-center py-3">
					<div class="flex-1">
						<div class="flex items-center">
							<h3 class="text-base font-medium text-gray-800">Книги</h3>
							<span class="status-badge status-inactive ml-3">Неактивна</span>
						</div>
						<div class="flex items-center mt-1 text-sm text-gray-500">
							<span>0 продукта</span>
							<a href="#" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
						</div>
					</div>
					<div class="flex items-center space-x-2 pr-4">
						<button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-edit-line"></i>
							</div>
						</button>
					</div>
				</div>
			</div>
		</div>
		<!-- Pagination -->
		<div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
			<div class="text-sm text-gray-600">
				Показани 1-10 от 15 категории
			</div>
			<div class="flex items-center space-x-2">
				<div class="relative">
					<button id="per-page-dropdown-btn" class="flex items-center justify-between px-3 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
						<span>10 на страница</span>
						<div class="w-5 h-5 flex items-center justify-center ml-2">
							<i class="ri-arrow-down-s-line"></i>
						</div>
					</button>
					<div id="per-page-dropdown" class="hidden absolute right-0 z-10 w-40 mt-1 bg-white border border-gray-200 rounded shadow-lg">
						<ul class="py-1">
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">10 на страница</li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">20 на страница</li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">50 на страница</li>
							<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Всички</li>
						</ul>
					</div>
				</div>
				<div class="flex">
					<button class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
						<div class="w-5 h-5 flex items-center justify-center">
							<i class="ri-arrow-left-s-line"></i>
						</div>
					</button>
					<button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-primary text-white">1</button>
					<button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">2</button>
					<button class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100">
						<div class="w-5 h-5 flex items-center justify-center">
							<i class="ri-arrow-right-s-line"></i>
						</div>
					</button>
				</div>
			</div>
		</div>
	</div>
</main></div><!-- Add/Edit Category Modal --><div id="category-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
<div class="bg-white rounded-lg w-full max-w-md mx-4">
	<div class="flex justify-between items-center p-6 border-b border-gray-200">
		<h3 class="text-lg font-semibold text-gray-800" id="category-modal-title">Добавяне на категория</h3>
		<button id="close-category-modal" class="text-gray-400 hover:text-gray-500">
			<div class="w-6 h-6 flex items-center justify-center">
				<i class="ri-close-line"></i>
			</div>
		</button>
	</div>
	<div class="p-6">
		<form id="category-form" class="space-y-4">
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">Име на категорията*</label>
				<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете име на категорията" required>
			</div>
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">Родителска категория</label>
				<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
					<option value="">Няма (основна категория)</option>
					<option value="1">Електроника</option>
					<option value="2">Дрехи</option>
					<option value="3">Дом и градина</option>
					<option value="4">Спорт</option>
				</select>
			</div>
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
				<textarea class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" rows="3" placeholder="Въведете описание на категорията"></textarea>
			</div>
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">URL (SEO)</label>
				<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="например: elektronika">
			</div>
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">Позиция</label>
				<input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="0">
			</div>
			<div class="flex items-center">
				<label class="inline-flex items-center">
					<span class="mr-2 text-sm font-medium text-gray-700">Статус:</span>
				</label>
				<label class="toggle-switch ml-2">
					<input type="checkbox" checked>
					<span class="toggle-slider"></span>
				</label>
				<span class="ml-2 text-sm text-gray-600">Активна</span>
			</div>
			<div class="flex justify-end space-x-2 mt-6">
				<button type="button" id="cancel-category" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Отказ</button>
				<button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Запази</button>
			</div>
		</form>
	</div>
</div></div><script>
document.addEventListener('DOMContentLoaded', function () { // Filter modal functionality
const filterBtn = document.getElementById('filter-btn');
const filterModal = document.getElementById('filter-modal');
const closeFilter = document.getElementById('close-filter');
const filterForm = document.getElementById('filter-form');
const resetFilter = document.getElementById('reset-filter');

if (filterBtn && filterModal && closeFilter && filterForm && resetFilter) {
filterBtn.addEventListener('click', function () {
filterModal.classList.remove('hidden');
document.body.style.overflow = 'hidden';
});

closeFilter.addEventListener('click', function () {
filterModal.classList.add('hidden');
document.body.style.overflow = 'auto';
});

filterModal.addEventListener('click', function (e) {
if (e.target === filterModal) {
filterModal.classList.add('hidden');
document.body.style.overflow = 'auto';
}
});

filterForm.addEventListener('submit', function (e) {
e.preventDefault();
filterModal.classList.add('hidden');
document.body.style.overflow = 'auto';
});

resetFilter.addEventListener('click', function () {
filterForm.reset();
});
}

// Category modal functionality
const addCategoryBtn = document.getElementById('add-category-btn');
const categoryModal = document.getElementById('category-modal');
const closeCategoryModal = document.getElementById('close-category-modal');
const cancelCategory = document.getElementById('cancel-category');
const categoryForm = document.getElementById('category-form');
const categoryModalTitle = document.getElementById('category-modal-title');
const editButtons = document.querySelectorAll('.ri-edit-line');

if (addCategoryBtn && categoryModal && closeCategoryModal && cancelCategory && categoryForm) {
addCategoryBtn.addEventListener('click', function () {
categoryModalTitle.textContent = 'Добавяне на категория';
categoryModal.classList.remove('hidden');
document.body.style.overflow = 'hidden';
});

closeCategoryModal.addEventListener('click', function () {
categoryModal.classList.add('hidden');
document.body.style.overflow = 'auto';
});

cancelCategory.addEventListener('click', function () {
categoryModal.classList.add('hidden');
document.body.style.overflow = 'auto';
});

categoryModal.addEventListener('click', function (e) {
if (e.target === categoryModal) {
categoryModal.classList.add('hidden');
document.body.style.overflow = 'auto';
}
});

categoryForm.addEventListener('submit', function (e) {
e.preventDefault();
categoryModal.classList.add('hidden');
document.body.style.overflow = 'auto';
// Here would be the logic to save the category
});

editButtons.forEach(btn => {
btn.parentElement.parentElement.addEventListener('click', function () {
categoryModalTitle.textContent = 'Редактиране на категория';
categoryModal.classList.remove('hidden');
document.body.style.overflow = 'hidden';
});
});
}

// Toggle sidebar
const toggleSidebar = document.getElementById('toggle-sidebar');
const sidebar = document.getElementById('sidebar');
const mobileMenu = document.getElementById('mobile-menu');

if (toggleSidebar && sidebar && mobileMenu) {
toggleSidebar.addEventListener('click', function () {
sidebar.classList.toggle('w-64');
sidebar.classList.toggle('w-20');
const sidebarItems = document.querySelectorAll('.sidebar-item span');
const sidebarLogo = document.getElementById('sidebar-logo');
sidebarItems.forEach(item => {
item.classList.toggle('hidden');
});
sidebarLogo.classList.toggle('hidden');
const icon = toggleSidebar.querySelector('i');
if (icon.classList.contains('ri-menu-fold-line')) {
icon.classList.remove('ri-menu-fold-line');
icon.classList.add('ri-menu-unfold-line');
} else {
icon.classList.remove('ri-menu-unfold-line');
icon.classList.add('ri-menu-fold-line');
}
});

mobileMenu.addEventListener('click', function () {
sidebar.classList.toggle('-translate-x-full');
});
}

// Status dropdown
const statusDropdownBtn = document.getElementById('status-dropdown-btn');
const statusDropdown = document.getElementById('status-dropdown');

if (statusDropdownBtn && statusDropdown) {
statusDropdownBtn.addEventListener('click', function () {
statusDropdown.classList.toggle('hidden');
});

// Select status option
const statusItems = statusDropdown.querySelectorAll('li');
statusItems.forEach(item => {
item.addEventListener('click', function () {
statusDropdownBtn.querySelector('span').textContent = this.textContent;
statusDropdown.classList.add('hidden');
});
});
}

// Sort dropdown
const sortDropdownBtn = document.getElementById('sort-dropdown-btn');
const sortDropdown = document.getElementById('sort-dropdown');

if (sortDropdownBtn && sortDropdown) {
sortDropdownBtn.addEventListener('click', function () {
sortDropdown.classList.toggle('hidden');
});

// Select sort option
const sortItems = sortDropdown.querySelectorAll('li');
sortItems.forEach(item => {
item.addEventListener('click', function () {
sortDropdownBtn.querySelector('span').textContent = this.textContent;
sortDropdown.classList.add('hidden');
});
});
}

// Per page dropdown
const perPageDropdownBtn = document.getElementById('per-page-dropdown-btn');
const perPageDropdown = document.getElementById('per-page-dropdown');

if (perPageDropdownBtn && perPageDropdown) {
perPageDropdownBtn.addEventListener('click', function () {
perPageDropdown.classList.toggle('hidden');
});

// Select per page option
const perPageItems = perPageDropdown.querySelectorAll('li');
perPageItems.forEach(item => {
item.addEventListener('click', function () {
perPageDropdownBtn.querySelector('span').textContent = this.textContent;
perPageDropdown.classList.add('hidden');
});
});
}

// Close dropdowns when clicking outside
document.addEventListener('click', function (event) {
if (statusDropdownBtn && statusDropdown && ! statusDropdownBtn.contains(event.target) && ! statusDropdown.contains(event.target)) {
statusDropdown.classList.add('hidden');
}
if (sortDropdownBtn && sortDropdown && ! sortDropdownBtn.contains(event.target) && ! sortDropdown.contains(event.target)) {
sortDropdown.classList.add('hidden');
}
if (perPageDropdownBtn && perPageDropdown && ! perPageDropdownBtn.contains(event.target) && ! perPageDropdown.contains(event.target)) {
perPageDropdown.classList.add('hidden');
}
});

// Drag and drop functionality (simplified)
const dragHandles = document.querySelectorAll('.drag-handle');
dragHandles.forEach(handle => {
handle.addEventListener('mouseenter', function () {
this.style.cursor = 'ns-resize';
});
handle.addEventListener('mouseleave', function () {
this.style.cursor = 'default';
});
});
});</script>
