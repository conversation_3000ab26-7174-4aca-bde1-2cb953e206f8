{{ header }}{{ column_left }}

<div class="container-fluid">
    <div class="page-header">
        <div class="container-fluid">
            <div class="float-end">
                <button type="submit" form="form-category" data-bs-toggle="tooltip" title="Запазване" class="btn btn-primary">
                    <i class="fa-solid fa-floppy-disk"></i>
                </button>
                <a href="{{ cancel }}" data-bs-toggle="tooltip" title="Отказ" class="btn btn-secondary">
                    <i class="fa-solid fa-reply"></i>
                </a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ol class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                    <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
                {% endfor %}
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <i class="fa-solid fa-pencil"></i> {{ text_form }}
            </div>
            <div class="card-body">
                <form id="form-category" action="{{ action }}" method="post" data-oc-toggle="ajax">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" href="#tab-general" data-bs-toggle="tab">Основни данни</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tab-data" data-bs-toggle="tab">Данни</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tab-seo" data-bs-toggle="tab">SEO</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tab-design" data-bs-toggle="tab">Дизайн</a>
                        </li>
                    </ul>

                    <div class="tab-content">
                        <!-- Основни данни -->
                        <div class="tab-pane fade show active" id="tab-general">
                            <ul class="nav nav-pills mb-3">
                                {% for language in languages %}
                                    <li class="nav-item">
                                        <a class="nav-link{% if loop.first %} active{% endif %}" href="#language-{{ language.language_id }}" data-bs-toggle="pill">
                                            <img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>

                            <div class="tab-content">
                                {% for language in languages %}
                                    <div class="tab-pane fade{% if loop.first %} show active{% endif %}" id="language-{{ language.language_id }}">
                                        <div class="row mb-3 required">
                                            <label for="input-name-{{ language.language_id }}" class="col-sm-2 col-form-label">Име на категорията</label>
                                            <div class="col-sm-10">
                                                <input type="text" name="category_description[{{ language.language_id }}][name]" value="{{ category_description[language.language_id] ? category_description[language.language_id].name : '' }}" placeholder="Име на категорията" id="input-name-{{ language.language_id }}" class="form-control" />
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <label for="input-description-{{ language.language_id }}" class="col-sm-2 col-form-label">Описание</label>
                                            <div class="col-sm-10">
                                                <div class="editor-container">
                                                    <textarea name="category_description[{{ language.language_id }}][description]" placeholder="Описание" id="input-description-{{ language.language_id }}" class="form-control">{{ category_description[language.language_id] ? category_description[language.language_id].description : '' }}</textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <label for="input-meta-title-{{ language.language_id }}" class="col-sm-2 col-form-label">Meta Title</label>
                                            <div class="col-sm-10">
                                                <input type="text" name="category_description[{{ language.language_id }}][meta_title]" value="{{ category_description[language.language_id] ? category_description[language.language_id].meta_title : '' }}" placeholder="Meta Title" id="input-meta-title-{{ language.language_id }}" class="form-control" />
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <label for="input-meta-description-{{ language.language_id }}" class="col-sm-2 col-form-label">Meta Description</label>
                                            <div class="col-sm-10">
                                                <textarea name="category_description[{{ language.language_id }}][meta_description]" rows="5" placeholder="Meta Description" id="input-meta-description-{{ language.language_id }}" class="form-control">{{ category_description[language.language_id] ? category_description[language.language_id].meta_description : '' }}</textarea>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <label for="input-meta-keyword-{{ language.language_id }}" class="col-sm-2 col-form-label">Meta Keywords</label>
                                            <div class="col-sm-10">
                                                <textarea name="category_description[{{ language.language_id }}][meta_keyword]" rows="5" placeholder="Meta Keywords" id="input-meta-keyword-{{ language.language_id }}" class="form-control">{{ category_description[language.language_id] ? category_description[language.language_id].meta_keyword : '' }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Данни -->
                        <div class="tab-pane fade" id="tab-data">
                            <div class="row mb-3">
                                <label for="input-parent" class="col-sm-2 col-form-label">Родителска категория</label>
                                <div class="col-sm-10">
                                    <select name="parent_id" id="input-parent" class="form-select">
                                        <option value="0">Няма</option>
                                        {% for category in categories %}
                                            <option value="{{ category.category_id }}"{% if parent_id == category.category_id %} selected{% endif %}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="input-image" class="col-sm-2 col-form-label">Изображение</label>
                                <div class="col-sm-10">
                                    <div class="card image">
                                        <img src="{{ image ? image : placeholder }}" alt="" title="" id="thumb-image" data-oc-placeholder="{{ placeholder }}" class="card-img-top" />
                                        <input type="hidden" name="image" value="{{ image }}" id="input-image" />
                                        <div class="card-body">
                                            <button type="button" data-oc-toggle="image" data-oc-target="#input-image" data-oc-thumb="#thumb-image" class="btn btn-primary btn-sm btn-block">
                                                <i class="fa-solid fa-pencil"></i> Редактиране
                                            </button>
                                            <button type="button" data-oc-toggle="clear" data-oc-target="#input-image" data-oc-thumb="#thumb-image" class="btn btn-warning btn-sm btn-block">
                                                <i class="fa-solid fa-trash-can"></i> Изчистване
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label class="col-sm-2 col-form-label">Показване в топ меню</label>
                                <div class="col-sm-10">
                                    <div class="form-check form-switch form-switch-lg">
                                        <input type="hidden" name="top" value="0" />
                                        <input type="checkbox" name="top" value="1" id="input-top" class="form-check-input"{% if top %} checked{% endif %} />
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="input-column" class="col-sm-2 col-form-label">Колони</label>
                                <div class="col-sm-10">
                                    <input type="text" name="column" value="{{ column }}" placeholder="Колони" id="input-column" class="form-control" />
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="input-sort-order" class="col-sm-2 col-form-label">Позиция</label>
                                <div class="col-sm-10">
                                    <input type="text" name="sort_order" value="{{ sort_order }}" placeholder="Позиция" id="input-sort-order" class="form-control" />
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label class="col-sm-2 col-form-label">Статус</label>
                                <div class="col-sm-10">
                                    <div class="form-check form-switch form-switch-lg">
                                        <input type="hidden" name="status" value="0" />
                                        <input type="checkbox" name="status" value="1" id="input-status" class="form-check-input"{% if status %} checked{% endif %} />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SEO -->
                        <div class="tab-pane fade" id="tab-seo">
                            <div class="alert alert-info">
                                <i class="fa-solid fa-info-circle"></i> SEO URL адресите трябва да бъдат уникални за всеки магазин и език.
                            </div>

                            {% for store in stores %}
                                <fieldset>
                                    <legend>{{ store.name }}</legend>
                                    {% for language in languages %}
                                        <div class="row mb-3">
                                            <label for="input-seo-url-{{ store.store_id }}-{{ language.language_id }}" class="col-sm-2 col-form-label">
                                                <img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}
                                            </label>
                                            <div class="col-sm-10">
                                                <input type="text" name="category_seo_url[{{ store.store_id }}][{{ language.language_id }}]" value="{{ category_seo_url[store.store_id][language.language_id] ? category_seo_url[store.store_id][language.language_id] : '' }}" placeholder="SEO URL" id="input-seo-url-{{ store.store_id }}-{{ language.language_id }}" class="form-control" />
                                            </div>
                                        </div>
                                    {% endfor %}
                                </fieldset>
                            {% endfor %}
                        </div>

                        <!-- Дизайн -->
                        <div class="tab-pane fade" id="tab-design">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <td class="text-start">Магазин</td>
                                            <td class="text-start">Избор</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for store in stores %}
                                            <tr>
                                                <td class="text-start">{{ store.name }}</td>
                                                <td class="text-start">
                                                    <div class="form-check">
                                                        <input type="checkbox" name="category_store[]" value="{{ store.store_id }}" id="input-store-{{ store.store_id }}" class="form-check-input"{% if store.store_id in category_store %} checked{% endif %} />
                                                        <label for="input-store-{{ store.store_id }}" class="form-check-label"></label>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" name="category_id" value="{{ category_id }}" />
                </form>
            </div>
        </div>
    </div>
</div>

{{ footer }}
