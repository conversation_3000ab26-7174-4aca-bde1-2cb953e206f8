<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Edit extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
        $this->loadStyles();
    }

    /**
     * Изпълнява подготовката на формата за редактиране на категория
     */
    public function execute() {
        $category_id = $this->requestGet('category_id', 0);
        
        if ($category_id) {
            $this->setTitle('Редакция на категория');
        } else {
            $this->setTitle('Добавяне на категория');
        }
        
        $this->initAdminData();
        $this->prepareCategoryForm();
        $this->renderTemplateWithDataAndOutput('catalog/category_form');
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме category-form.js, ако съществува
        $categoryFormUrl = $base . 'backend_js/category-form.js';
        $categoryFormPath = DIR_THEME . 'Backend/View/Javascript/category-form.js';
        
        if (file_exists($categoryFormPath)) {
            $lastModified = filemtime($categoryFormPath);
            $categoryFormUrl .= '?v=' . $lastModified;
            $this->document->addScript($categoryFormUrl, 'footer');
        }
    }

    /**
     * Зарежда необходимите CSS файлове
     */
    protected function loadStyles() {
        // Зареждане на допълнителни стилове, ако са необходими
    }

    /**
     * Подготвя формата за редактиране/добавяне на категория
     */
    public function prepareCategoryForm() {
        $this->loadLanguage('catalog/category');

        // Зареждане на модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'localisation/language' => 'languageModel',
            'setting/store' => 'storeModel',
            'tool/image' => 'imageModel'
        ]);

        $category_id = $this->requestGet('category_id', 0);
        $category_info = [];

        if ($category_id && $this->request->server['REQUEST_METHOD'] != 'POST') {
            $category_info = $this->categoryModel->getCategory($category_id);
        }

        // Подготовка на основните данни
        $this->prepareCategoryBasicData($category_id, $category_info);
        
        // Подготовка на езиковите данни
        $this->prepareCategoryLanguageData($category_id, $category_info);
        
        // Подготовка на SEO данни
        $this->prepareCategorySeoData($category_id, $category_info);
        
        // Подготовка на данни за изображения
        $this->prepareCategoryImageData($category_id, $category_info);
        
        // Подготовка на данни за магазини
        $this->prepareCategoryStoreData($category_id, $category_info);
        
        // Подготовка на данни за родителски категории
        $this->prepareCategoryParentData($category_id, $category_info);
        
        // Подготовка на данни за формата
        $this->prepareCategoryFormData($category_id);
    }

    /**
     * Подготвя основните данни за категорията
     */
    private function prepareCategoryBasicData($category_id, $category_info) {
        // Основни полета
        $basic_fields = [
            'category_id' => $category_id,
            'parent_id' => 0,
            'sort_order' => 0,
            'status' => 1,
            'top' => 0,
            'column' => 1
        ];

        foreach ($basic_fields as $field => $default) {
            if (isset($this->request->post[$field])) {
                $this->setData($field, $this->request->post[$field]);
            } elseif (!empty($category_info[$field])) {
                $this->setData($field, $category_info[$field]);
            } else {
                $this->setData($field, $default);
            }
        }
    }

    /**
     * Подготвя езиковите данни за категорията
     */
    private function prepareCategoryLanguageData($category_id, $category_info) {
        // Получаване на всички езици
        $languages = $this->languageModel->getLanguages();
        $this->setData('languages', $languages);

        // Подготовка на описанията за всички езици
        $category_description = [];

        foreach ($languages as $language) {
            $language_id = $language['language_id'];
            
            if (isset($this->request->post['category_description'][$language_id])) {
                $category_description[$language_id] = $this->request->post['category_description'][$language_id];
            } elseif ($category_id) {
                $description = $this->categoryModel->getCategoryDescriptions($category_id);
                if (isset($description[$language_id])) {
                    $category_description[$language_id] = $description[$language_id];
                } else {
                    $category_description[$language_id] = [
                        'name' => '',
                        'description' => '',
                        'meta_title' => '',
                        'meta_description' => '',
                        'meta_keyword' => ''
                    ];
                }
            } else {
                $category_description[$language_id] = [
                    'name' => '',
                    'description' => '',
                    'meta_title' => '',
                    'meta_description' => '',
                    'meta_keyword' => ''
                ];
            }
        }

        $this->setData('category_description', $category_description);
    }

    /**
     * Подготвя SEO данните за категорията
     */
    private function prepareCategorySeoData($category_id, $category_info) {
        // Получаване на всички магазини
        $stores = $this->storeModel->getStores();
        $all_stores = [['store_id' => 0, 'name' => $this->getConfig('config_name')]];
        $all_stores = array_merge($all_stores, $stores);

        // Получаване на всички езици
        $languages = $this->data['languages'];

        // Подготовка на SEO URL данни
        $category_seo_url = [];

        foreach ($all_stores as $store) {
            foreach ($languages as $language) {
                $store_id = $store['store_id'];
                $language_id = $language['language_id'];
                
                if (isset($this->request->post['category_seo_url'][$store_id][$language_id])) {
                    $category_seo_url[$store_id][$language_id] = $this->request->post['category_seo_url'][$store_id][$language_id];
                } elseif ($category_id) {
                    $seo_urls = $this->categoryModel->getCategorySeoUrls($category_id);
                    if (isset($seo_urls[$store_id][$language_id])) {
                        $category_seo_url[$store_id][$language_id] = $seo_urls[$store_id][$language_id];
                    } else {
                        $category_seo_url[$store_id][$language_id] = '';
                    }
                } else {
                    $category_seo_url[$store_id][$language_id] = '';
                }
            }
        }

        $this->setData('category_seo_url', $category_seo_url);
        $this->setData('stores', $all_stores);
    }

    /**
     * Подготвя данните за изображения
     */
    private function prepareCategoryImageData($category_id, $category_info) {
        // Основно изображение
        if (isset($this->request->post['image'])) {
            $this->setData('image', $this->request->post['image']);
        } elseif (!empty($category_info['image'])) {
            $this->setData('image', $category_info['image']);
        } else {
            $this->setData('image', '');
        }

        // Placeholder изображение
        $this->setData('placeholder', $this->imageModel->resize('no_image.png', 100, 100));
    }

    /**
     * Подготвя данните за магазини
     */
    private function prepareCategoryStoreData($category_id, $category_info) {
        // Категория към магазини
        if (isset($this->request->post['category_store'])) {
            $category_store = $this->request->post['category_store'];
        } elseif ($category_id) {
            $category_store = $this->categoryModel->getCategoryStores($category_id);
        } else {
            $category_store = [0]; // По подразбиране главният магазин
        }

        $this->setData('category_store', $category_store);
    }

    /**
     * Подготвя данните за родителски категории
     */
    private function prepareCategoryParentData($category_id, $category_info) {
        // Получаване на всички категории за родителски dropdown
        $categories = $this->categoryModel->getCategories(['sort' => 'name']);
        
        // Филтриране на текущата категория от списъка (не може да бъде родител на себе си)
        if ($category_id) {
            $categories = array_filter($categories, function($cat) use ($category_id) {
                return $cat['category_id'] != $category_id;
            });
        }

        $this->setData('categories', $categories);
    }

    /**
     * Подготвя данните за формата
     */
    private function prepareCategoryFormData($category_id) {
        // URL адреси
        $this->setData([
            'action' => $this->getAdminLink('catalog/category/save'),
            'cancel' => $this->getAdminLink('catalog/category'),
            'upload' => $this->getAdminLink('common/filemanager'),
        ]);

        // Токен за сигурност
        $this->setData('user_token', $this->session->data['user_token']);
        
        // Активен език ID
        $this->setData('active_language_id', $this->getLanguageId());
    }
}
