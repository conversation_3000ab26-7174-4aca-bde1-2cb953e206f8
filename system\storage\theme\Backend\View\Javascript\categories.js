/**
 * JavaScript модул за управление на категории
 * Отговаря за drag & drop функционалност, модали и филтри
 */
(function() {
    'use strict';

    console.log('[Categories] Module loading...');

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Проверяваме дали BackendModule е зареден
        if (typeof BackendModule !== 'undefined') {
            console.log('[Categories] BackendModule found, initializing categories...');
            BackendModule.initCategories();
        } else {
            console.error('[Categories] BackendModule not found! Make sure backend.js is loaded first.');
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        console.log('[Categories] Extending BackendModule with categories functionality...');
        Object.assign(BackendModule, {
            // Конфигурация за категории
            categories: {
                draggedElement: null,
                draggedData: null,
                dropZones: []
            },

            /**
             * Инициализация на модула за категории
             */
            initCategories: function() {
                this.setupCategoryEventListeners();
                this.initializeCategoryDragAndDrop();
                this.initializeCategoryHierarchy();
                this.logDev && this.logDev('Categories module initialized');
            },

            /**
             * Инициализация на йерархичната функционалност
             */
            initializeCategoryHierarchy: function() {
                this.logDev && this.logDev('Initializing category hierarchy...');
                this.setupCategoryExpandCollapse();
                this.loadCategoryAjaxUrls();
            },

            /**
             * Зарежда AJAX URL адресите
             */
            loadCategoryAjaxUrls: function() {
                const userToken = this.getUserToken();
                this.categoryAjaxUrls = {
                    loadSubcategories: window.ajaxLoadSubcategoriesUrl || 'index.php?route=catalog/category/ajax/loadSubcategories&user_token=' + userToken,
                    updateSortOrder: window.ajaxUpdateSortOrderUrl || 'index.php?route=catalog/category/ajax/updateSortOrder&user_token=' + userToken,
                    getCategoryInfo: window.ajaxGetCategoryInfoUrl || 'index.php?route=catalog/category/ajax/getCategoryInfo&user_token=' + userToken
                };
            },

            /**
             * Настройва expand/collapse функционалност
             */
            setupCategoryExpandCollapse: function() {
                const categories = document.querySelectorAll('.category-item');

                categories.forEach(categoryRow => {
                    const categoryId = categoryRow.dataset.categoryId ||
                                     categoryRow.querySelector('[data-category-id]')?.dataset.categoryId;

                    if (categoryId && this.categoryHasSubcategories(categoryRow)) {
                        this.addCategoryExpandButton(categoryRow, categoryId);
                    }
                });
            },

            /**
             * Проверява дали категорията има подкатегории
             */
            categoryHasSubcategories: function(categoryRow) {
                // Проверяваме за data атрибут или клас, който показва наличие на подкатегории
                return categoryRow.dataset.hasSubcategories === 'true' ||
                       categoryRow.classList.contains('has-subcategories');
            },

            /**
             * Добавя expand/collapse бутон към категория
             */
            addCategoryExpandButton: function(categoryRow, categoryId) {
                const expandButton = document.createElement('button');
                expandButton.className = 'category-expand-btn w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700 mr-2 transition-colors';
                expandButton.innerHTML = '<i class="ri-arrow-right-s-line"></i>';
                expandButton.dataset.categoryId = categoryId;
                expandButton.dataset.expanded = 'false';
                expandButton.title = 'Покажи подкатегории';

                // Добавяме бутона в началото на flex контейнера
                const flexContainer = categoryRow.querySelector('.flex-1');
                if (flexContainer) {
                    flexContainer.insertBefore(expandButton, flexContainer.firstChild);
                }

                // Event listener за expand/collapse
                expandButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleCategoryExpand(expandButton, categoryId);
                });
            },

            /**
             * Превключва expand/collapse състоянието на категория
             */
            toggleCategoryExpand: function(button, categoryId) {
                const isExpanded = button.dataset.expanded === 'true';

                if (isExpanded) {
                    this.collapseCategorySubcategories(button, categoryId);
                } else {
                    this.expandCategorySubcategories(button, categoryId);
                }
            },

            /**
             * Разширява категорията и зарежда подкатегориите
             */
            expandCategorySubcategories: function(button, categoryId) {
                this.logDev && this.logDev('Expanding category:', categoryId);

                // Визуални промени
                button.dataset.expanded = 'true';
                button.querySelector('i').className = 'ri-arrow-down-s-line';
                button.title = 'Скрий подкатегории';

                // Показваме loading индикатор
                this.showCategoryLoading(button);

                // AJAX заявка за зареждане на подкатегориите
                this.loadCategorySubcategories(categoryId)
                    .then(subcategories => {
                        this.hideCategoryLoading(button);
                        this.renderCategorySubcategories(button, categoryId, subcategories);
                    })
                    .catch(error => {
                        this.hideCategoryLoading(button);
                        this.logDev && this.logDev('Error loading subcategories:', error);
                        this.showNotification('Грешка при зареждане на подкатегориите', 'error');
                    });
            },

            /**
             * Свива категорията и скрива подкатегориите
             */
            collapseCategorySubcategories: function(button, categoryId) {
                this.logDev && this.logDev('Collapsing category:', categoryId);

                // Визуални промени
                button.dataset.expanded = 'false';
                button.querySelector('i').className = 'ri-arrow-right-s-line';
                button.title = 'Покажи подкатегории';

                // Премахваме подкатегориите от DOM
                this.removeCategorySubcategories(categoryId);
            },

            /**
             * Зарежда подкатегориите чрез AJAX
             */
            loadCategorySubcategories: function(parentId) {
                const url = this.categoryAjaxUrls.loadSubcategories;

                const formData = new FormData();
                formData.append('parent_id', parentId);
                
                return fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.error || 'Неизвестна грешка');
                    }
                    return data.subcategories || [];
                });
            },

            /**
             * Рендерира подкатегориите в DOM
             */
            renderCategorySubcategories: function(button, parentId, subcategories) {

                const parentRow = button.closest('.category-item');
                if (!parentRow) return;

                // Създаваме контейнер за подкатегориите
                const subcategoriesContainer = document.createElement('div');
                subcategoriesContainer.className = 'subcategories-container';
                subcategoriesContainer.dataset.parentId = parentId;

                // Рендерираме всяка подкатегория
                subcategories.forEach((subcategory, index) => {
                    // const subcategoryRow = this.createCategorySubcategoryRow(subcategory, parentId, index + 1);
                    const subcategoryRow = this.createCategorySubcategoryRow(subcategory, parentId, subcategory.level);
                    subcategoriesContainer.appendChild(subcategoryRow);
                });

                // Вмъкваме контейнера след родителската категория
                parentRow.parentNode.insertBefore(subcategoriesContainer, parentRow.nextSibling);

                // Анимация за показване
                this.animateCategoryExpand(subcategoriesContainer);
            },

            /**
             * Създава ред за подкатегория
             */
            createCategorySubcategoryRow: function(subcategory, parentId, level) {
                const row = document.createElement('div');

                // Използваме същите класове като основните категории, но добавяме subcategory-item
                row.className = 'category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all';
                row.dataset.categoryId = subcategory.category_id;
                row.dataset.parentId = parentId;
                row.dataset.sortOrder = subcategory.sort_order || 0;
                row.dataset.level = level;
                if (subcategory.has_subcategories) {
                    row.dataset.hasSubcategories = 'true';
                }

                // Изчисляваме отстъпа според нивото (20px за всяко ниво)
                const indentWidth = level * 20;

                // Създаваме HTML структурата идентична с основните категории
                row.innerHTML = `
                    <!-- Expand/Collapse Button с отстъп -->
                    ${subcategory.has_subcategories ?
                        `<button class="category-expand-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600"
                                style="margin-left: ${2 + indentWidth}px;"
                                data-category-id="${subcategory.category_id}"
                                data-expanded="false">
                            <i class="ri-arrow-right-s-line"></i>
                        </button>` :
                        `<div class="w-8 h-8" style="margin-left: ${2 + indentWidth}px;"></div>`
                    }

                    <!-- Drag Handle -->
                    <div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400 cursor-grab">
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-drag-move-2-line"></i>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="flex-1 flex items-center py-3">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <h3 class="text-base font-medium text-gray-800">${subcategory.name}</h3>
                                <span class="status-badge ${' ' + subcategory.status_class} ml-3">${subcategory.status_text}</span>
                            </div>
                            <div class="flex items-center mt-1 text-sm text-gray-500">
                                <span>${subcategory.product_count} продукта</span>
                                ${subcategory.has_subcategories ?
                                    `<span class="ml-2">• ${subcategory.subcategory_count || 0} подкатегории</span>` :
                                    ''
                                }
                                <a href="${subcategory.products_url || '#'}" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 pr-4">
                            <a href="${subcategory.edit}" class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </a>
                            <button class="w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full"
                                    title="Изтриване" onclick="confirmDelete('${subcategory.delete || '#'}')">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-delete-bin-line"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                `;

                // Добавяме expand функционалност ако има подкатегории
                if (subcategory.has_subcategories) {
                    const expandBtn = row.querySelector('.category-expand-btn');
                    if (expandBtn) {
                        expandBtn.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            this.toggleCategoryExpand(expandBtn, subcategory.category_id);
                        });
                    }
                }

                // Правим елемента draggable
                this.makeCategoryDraggable(row);

                return row;
            },

            /**
             * Настройка на всички event listeners за категории
             */
            setupCategoryEventListeners: function() {
                this.setupCategoryFilterModal();
                this.setupCategoryModal();
                this.setupCategoryDropdowns();
            },

            /**
             * Премахва подкатегориите от DOM
             */
            removeCategorySubcategories: function(parentId) {
                const container = document.querySelector(`.subcategories-container[data-parent-id="${parentId}"]`);
                if (container) {
                    this.animateCategoryCollapse(container, () => {
                        container.remove();
                    });
                }
            },

            /**
             * Анимация за разширяване на категория
             */
            animateCategoryExpand: function(container) {
                container.style.maxHeight = '0';
                container.style.overflow = 'hidden';
                container.style.transition = 'max-height 0.3s ease-out';

                // Принудително reflow
                container.offsetHeight;

                // Изчисляваме височината
                container.style.maxHeight = container.scrollHeight + 'px';

                // Премахваме ограничението след анимацията
                setTimeout(() => {
                    container.style.maxHeight = '';
                    container.style.overflow = '';
                    container.style.transition = '';
                }, 300);
            },

            /**
             * Анимация за свиване на категория
             */
            animateCategoryCollapse: function(container, callback) {
                container.style.maxHeight = container.scrollHeight + 'px';
                container.style.overflow = 'hidden';
                container.style.transition = 'max-height 0.3s ease-in';

                // Принудително reflow
                container.offsetHeight;

                container.style.maxHeight = '0';

                setTimeout(() => {
                    if (callback) callback();
                }, 300);
            },

            /**
             * Показва loading индикатор за категория
             */
            showCategoryLoading: function(button) {
                const icon = button.querySelector('i');
                if (icon) {
                    icon.className = 'ri-loader-4-line animate-spin';
                }
            },

            /**
             * Скрива loading индикатора за категория
             */
            hideCategoryLoading: function(button) {
                const icon = button.querySelector('i');
                if (icon && button.dataset.expanded === 'true') {
                    icon.className = 'ri-arrow-down-s-line';
                } else if (icon) {
                    icon.className = 'ri-arrow-right-s-line';
                }
            },

            /**
             * Показва notification съобщение
             */
            showNotification: function(message, type = 'info') {
                // Използваме съществуващата notification система ако има
                if (typeof this.showToast === 'function') {
                    this.showToast(message, type);
                } else {
                    // Fallback към console
                    console.log(`[${type.toUpperCase()}] ${message}`);
                }
            },

            /**
             * Настройка на филтър модала за категории
             */
            setupCategoryFilterModal: function() {
                const filterBtn = document.getElementById('filter-btn');
                const filterModal = document.getElementById('filter-modal');
                const closeFilter = document.getElementById('close-filter');
                const filterForm = document.getElementById('filter-form');
                const resetFilter = document.getElementById('reset-filter');

                if (filterBtn && filterModal) {
                    filterBtn.addEventListener('click', () => {
                        this.openCategoryModal(filterModal);
                    });
                }

                if (closeFilter && filterModal) {
                    closeFilter.addEventListener('click', () => {
                        this.closeCategoryModal(filterModal);
                    });
                }

                if (filterModal) {
                    filterModal.addEventListener('click', (e) => {
                        if (e.target === filterModal) {
                            this.closeCategoryModal(filterModal);
                        }
                    });
                }

                if (filterForm) {
                    filterForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCategoryFilterSubmit(filterForm);
                        if (filterModal) {
                            this.closeCategoryModal(filterModal);
                        }
                    });
                }

                if (resetFilter && filterForm) {
                    resetFilter.addEventListener('click', () => {
                        filterForm.reset();
                        this.logDev && this.logDev('Filter form reset');
                    });
                }
            },

            /**
             * Настройка на категория модала
             */
            setupCategoryModal: function() {
                const addCategoryBtn = document.getElementById('add-category-btn');
                const categoryModal = document.getElementById('category-modal');
                const closeCategoryModal = document.getElementById('close-category-modal');
                const cancelCategory = document.getElementById('cancel-category');
                const categoryForm = document.getElementById('category-form');
                const categoryModalTitle = document.getElementById('category-modal-title');
                const editButtons = document.querySelectorAll('.ri-edit-line');

                if (addCategoryBtn && categoryModal) {
                    addCategoryBtn.addEventListener('click', () => {
                        if (categoryModalTitle) {
                            categoryModalTitle.textContent = 'Добавяне на категория';
                        }
                        this.openCategoryModal(categoryModal);
                    });
                }

                if (closeCategoryModal && categoryModal) {
                    closeCategoryModal.addEventListener('click', () => {
                        this.closeCategoryModal(categoryModal);
                    });
                }

                if (cancelCategory && categoryModal) {
                    cancelCategory.addEventListener('click', () => {
                        this.closeCategoryModal(categoryModal);
                    });
                }

                if (categoryModal) {
                    categoryModal.addEventListener('click', (e) => {
                        if (e.target === categoryModal) {
                            this.closeCategoryModal(categoryModal);
                        }
                    });
                }

                if (categoryForm) {
                    categoryForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCategorySubmit(categoryForm);
                        if (categoryModal) {
                            this.closeCategoryModal(categoryModal);
                        }
                    });
                }

                // Setup edit buttons
                editButtons.forEach(btn => {
                    const editLink = btn.closest('a');
                    if (editLink) {
                        editLink.addEventListener('click', (e) => {
                            e.preventDefault();
                            if (categoryModalTitle) {
                                categoryModalTitle.textContent = 'Редактиране на категория';
                            }
                            if (categoryModal) {
                                this.openCategoryModal(categoryModal);
                            }
                        });
                    }
                });
            },

            /**
             * Настройка на dropdown менютата за категории
             */
            setupCategoryDropdowns: function() {
                const dropdownButtons = document.querySelectorAll('[data-dropdown-toggle]');
                
                dropdownButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const targetId = button.getAttribute('data-dropdown-toggle');
                        const dropdown = document.getElementById(targetId);
                        
                        if (dropdown) {
                            // Затваряне на други dropdown менюта
                            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                                if (menu !== dropdown) {
                                    menu.classList.add('hidden');
                                }
                            });
                            
                            // Toggle на текущото dropdown меню
                            dropdown.classList.toggle('hidden');
                        }
                    });
                });

                // Затваряне на dropdown менютата при клик извън тях
                document.addEventListener('click', () => {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                });
            },

            /**
             * Инициализация на drag & drop функционалност за категории
             */
            initializeCategoryDragAndDrop: function() {
                const categoryRows = document.querySelectorAll('.category-item');

                if (categoryRows.length > 0) {
                    this.setupCategoryDragAndDrop();
                }
            },

            /**
             * Настройва drag & drop за всички категории (включително динамично заредени)
             */
            setupCategoryDragAndDrop: function() {
                // Премахваме стари event listeners
                this.removeCategoryDragAndDropListeners();

                // Добавяме нови за всички категории
                const categoryRows = document.querySelectorAll('.category-item');

                categoryRows.forEach(row => {
                    this.makeCategoryDraggable(row);
                });
            },

            /**
             * Прави категория draggable
             */
            makeCategoryDraggable: function(row) {
                // Правим реда draggable
                row.draggable = true;
                row.classList.add('drag-ready');

                // Добавяме event listeners
                row.addEventListener('dragstart', (e) => this.handleCategoryDragStart(e));
                row.addEventListener('dragend', (e) => this.handleCategoryDragEnd(e));
                row.addEventListener('dragover', (e) => this.handleCategoryDragOver(e));
                row.addEventListener('dragenter', (e) => this.handleCategoryDragEnter(e));
                row.addEventListener('dragleave', (e) => this.handleCategoryDragLeave(e));
                row.addEventListener('drop', (e) => this.handleCategoryDrop(e));
            },

            /**
             * Премахва drag & drop event listeners
             */
            removeCategoryDragAndDropListeners: function() {
                const categoryRows = document.querySelectorAll('.category-item.drag-ready');

                categoryRows.forEach(row => {
                    row.draggable = false;
                    row.classList.remove('drag-ready');

                    // Клониране на елемента за премахване на всички event listeners
                    const newRow = row.cloneNode(true);
                    row.parentNode.replaceChild(newRow, row);
                });
            },

            /**
             * Обработка на започване на drag операция
             */
            handleCategoryDragStart: function(e) {
                this.categories.draggedElement = e.target.closest('.category-item');
                this.categories.draggedData = {
                    id: this.getCategoryId(this.categories.draggedElement),
                    parentId: this.getCategoryParentId(this.categories.draggedElement),
                    level: this.getCategoryLevel(this.categories.draggedElement),
                    name: this.getCategoryName(this.categories.draggedElement)
                };

                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', e.target.outerHTML);

                this.logDev && this.logDev('Drag started for category:', this.categories.draggedData);
            },

            /**
             * Получава ID на категорията от DOM елемент
             */
            getCategoryId: function(element) {
                return element.dataset.categoryId ||
                       element.querySelector('[data-category-id]')?.dataset.categoryId;
            },

            /**
             * Получава parent ID на категорията от DOM елемент
             */
            getCategoryParentId: function(element) {
                return element.dataset.parentId || '0';
            },

            /**
             * Получава нивото на категорията от DOM елемент
             */
            getCategoryLevel: function(element) {
                return parseInt(element.dataset.level || '0');
            },

            /**
             * Получава името на категорията от DOM елемент
             */
            getCategoryName: function(element) {
                const nameElement = element.querySelector('.font-medium');
                return nameElement ? nameElement.textContent.trim() : '';
            },

            /**
             * Обработка на завършване на drag операция
             */
            handleCategoryDragEnd: function(e) {
                e.target.classList.remove('dragging');
                this.hideAllCategoryDropIndicators();
                
                // Почистване на drop zone класове
                document.querySelectorAll('.category-row').forEach(row => {
                    row.classList.remove('drop-zone-active', 'drop-zone-hover');
                });
                
                this.logDev && this.logDev('Drag ended');
            },

            /**
             * Обработка на drag over събития
             */
            handleCategoryDragOver: function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                
                const targetRow = e.target.closest('.category-row');
                if (targetRow && this.canCategoryDropOn(targetRow)) {
                    const dropPosition = this.getCategoryDropPosition(e, targetRow);
                    this.showCategoryDropIndicator(targetRow, dropPosition);
                }
            },

            /**
             * Обработка на drag enter събития
             */
            handleCategoryDragEnter: function(e) {
                e.preventDefault();
                const targetRow = e.target.closest('.category-row');
                if (targetRow && this.canCategoryDropOn(targetRow)) {
                    targetRow.classList.add('drop-zone-active');
                }
            },

            /**
             * Обработка на drag leave събития
             */
            handleCategoryDragLeave: function(e) {
                const targetRow = e.target.closest('.category-row');
                if (targetRow) {
                    targetRow.classList.remove('drop-zone-active', 'drop-zone-hover');
                }
            },

            /**
             * Обработка на drop операции
             */
            handleCategoryDrop: function(e) {
                e.preventDefault();

                const targetRow = e.target.closest('.category-item');
                if (!targetRow || !this.canCategoryDropOn(targetRow)) {
                    this.hideAllCategoryDropIndicators();
                    return;
                }

                const dropPosition = this.getCategoryDropPosition(e, targetRow);
                const targetData = {
                    id: this.getCategoryId(targetRow),
                    parentId: this.getCategoryParentId(targetRow),
                    level: this.getCategoryLevel(targetRow),
                    name: this.getCategoryName(targetRow)
                };

                this.performCategoryMove(targetData, dropPosition);
            },

            /**
             * Проверка дали може да се drop върху елемента
             */
            canCategoryDropOn: function(targetRow) {
                if (!this.categories.draggedElement || !targetRow) {
                    return false;
                }

                const draggedId = this.categories.draggedData.id;
                const targetId = this.getCategoryId(targetRow);

                // Не може да drop върху себе си
                if (draggedId === targetId) {
                    return false;
                }

                // Не може да премести родителска категория в собствена подкатегория
                if (this.isCategoryDescendant(targetId, draggedId)) {
                    return false;
                }

                return true;
            },

            /**
             * Проверява дали targetId е наследник на parentId
             */
            isCategoryDescendant: function(targetId, parentId) {
                // Намираме всички подкатегории на parentId
                const subcategories = document.querySelectorAll(`[data-parent-id="${parentId}"]`);

                for (let subcategory of subcategories) {
                    const subcategoryId = this.getCategoryId(subcategory);

                    if (subcategoryId === targetId) {
                        return true;
                    }

                    // Рекурсивна проверка
                    if (this.isCategoryDescendant(targetId, subcategoryId)) {
                        return true;
                    }
                }

            },

            /**
             * Проверка дали targetRow е наследник на parentRow
             */
            isCategoryDescendant: function(targetRow, parentRow) {
                const parentId = this.getCategoryId(parentRow);
                const targetParentId = this.getCategoryParentId(targetRow);

                if (targetParentId === parentId) {
                    return true;
                }

                // Рекурсивна проверка нагоре по йерархията
                const targetParentRow = document.querySelector(`[data-category-id="${targetParentId}"]`);
                if (targetParentRow && targetParentRow !== targetRow) {
                    return this.isCategoryDescendant(targetParentRow, parentRow);
                }

                return false;
            },

            /**
             * Определяне на позицията за drop (before/after/inside)
             */
            getCategoryDropPosition: function(e, targetRow) {
                const rect = targetRow.getBoundingClientRect();
                const y = e.clientY - rect.top;
                const height = rect.height;

                if (y < height * 0.25) {
                    return 'before';
                } else if (y > height * 0.75) {
                    return 'after';
                } else {
                    return 'inside';
                }
            },

            /**
             * Показване на drop индикатори
             */
            showCategoryDropIndicator: function(targetRow, position) {
                this.hideAllCategoryDropIndicators();

                const indicator = document.createElement('div');
                indicator.className = `drop-indicator drop-indicator-${position}`;
                indicator.id = 'category-drop-indicator';

                if (position === 'before') {
                    targetRow.parentNode.insertBefore(indicator, targetRow);
                } else if (position === 'after') {
                    targetRow.parentNode.insertBefore(indicator, targetRow.nextSibling);
                } else if (position === 'inside') {
                    indicator.className += ' drop-indicator-inside';
                    targetRow.appendChild(indicator);
                }
            },

            /**
             * Скриване на всички drop индикатори
             */
            hideAllCategoryDropIndicators: function() {
                const indicators = document.querySelectorAll('#category-drop-indicator');
                indicators.forEach(indicator => indicator.remove());
            },

            /**
             * Изпълнение на преместването на категория
             */
            performCategoryMove: function(targetData, dropPosition) {
                let newParentId = 0;
                let newSortOrder = 0;

                if (dropPosition === 'inside') {
                    newParentId = targetData.id;
                    newSortOrder = 1;
                } else {
                    newParentId = targetData.parentId;
                    const targetSortOrder = parseInt(targetData.sortOrder || 0);
                    newSortOrder = dropPosition === 'before' ? targetSortOrder : targetSortOrder + 1;
                }

                // Данни за AJAX заявката
                const moveData = {
                    category_id: this.categories.draggedData.id,
                    parent_id: newParentId,
                    sort_order: newSortOrder,
                    old_parent_id: this.categories.draggedData.parentId
                };

                this.logDev && this.logDev('Moving category:', moveData);

                // Показваме loading
                this.showCategoryMoveLoading();

                // AJAX заявка за обновяване на позицията
                this.updateCategorySortOrder(moveData)
                    .then(response => {
                        if (response.success) {
                            this.showNotification('Категорията е преместена успешно', 'success');
                            this.refreshCategoryList();
                        } else {
                            throw new Error(response.error || 'Неизвестна грешка');
                        }
                    })
                    .catch(error => {
                        this.logDev && this.logDev('Error moving category:', error);
                        this.showNotification('Грешка при преместване: ' + error.message, 'error');
                    })
                    .finally(() => {
                        this.hideCategoryMoveLoading();
                        this.hideAllCategoryDropIndicators();
                    });
            },

            /**
             * AJAX заявка за обновяване на sort order
             */
            updateCategorySortOrder: function(data) {
                const url = this.categoryAjaxUrls.updateSortOrder;

                return fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                });
            },

            /**
             * Показва loading за move операция
             */
            showCategoryMoveLoading: function() {
                // Можем да добавим loading overlay или spinner
                this.logDev && this.logDev('Showing move loading...');
            },

            /**
             * Скрива loading за move операция
             */
            hideCategoryMoveLoading: function() {
                this.logDev && this.logDev('Hiding move loading...');
            },

            /**
             * Обновява списъка с категории
             */
            refreshCategoryList: function() {
                // Презареждаме страницата за да видим промените
                window.location.reload();
            },

            /**
             * Скрива всички drop индикатори
             */
            hideAllCategoryDropIndicators: function() {
                const indicators = document.querySelectorAll('.drop-indicator');
                indicators.forEach(indicator => indicator.remove());

                const dropZones = document.querySelectorAll('.drop-zone-active');
                dropZones.forEach(zone => zone.classList.remove('drop-zone-active'));
            },



            /**
             * Обработка на филтър форма
             */
            handleCategoryFilterSubmit: function(form) {
                const formData = new FormData(form);
                const params = new URLSearchParams();

                for (let [key, value] of formData.entries()) {
                    if (value.trim() !== '') {
                        params.append(key, value);
                    }
                }

                // Добавяне на user_token
                if (this.config.userToken) {
                    params.append('user_token', this.config.userToken);
                }

                const url = form.action + '?' + params.toString();
                window.location.href = url;
            },

            /**
             * Обработка на категория форма
             */
            handleCategorySubmit: function(form) {
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert(data.error || 'Възникна грешка при запазването.');
                    }
                })
                .catch(error => {
                    console.error('Form submission error:', error);
                    alert('Възникна грешка при изпращането на формата.');
                });
            },

            /**
             * Отваряне на модал за категории
             */
            openCategoryModal: function(modal) {
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                }
            },

            /**
             * Затваряне на модал за категории
             */
            closeCategoryModal: function(modal) {
                if (modal) {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    document.body.style.overflow = '';
                }
            }
        });

        console.log('[Categories] BackendModule extended successfully');
    } else {
        console.error('[Categories] BackendModule not available for extension');
    }

    console.log('[Categories] Module loaded');
})();
