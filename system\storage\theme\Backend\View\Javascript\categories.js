/**
 * JavaScript модул за управление на категории
 * Отговаря за drag & drop функционалност, модали и филтри
 */
(function() {
    'use strict';

    console.log('[Categories] Module loading...');

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Проверяваме дали BackendModule е зареден
        if (typeof BackendModule !== 'undefined') {
            console.log('[Categories] BackendModule found, initializing categories...');
            BackendModule.initCategories();
        } else {
            console.error('[Categories] BackendModule not found! Make sure backend.js is loaded first.');
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        console.log('[Categories] Extending BackendModule with categories functionality...');
        Object.assign(BackendModule, {
            // Конфигурация за категории
            categories: {
                draggedElement: null,
                draggedData: null,
                dropZones: []
            },

            /**
             * Инициализация на модула за категории
             */
            initCategories: function() {
                this.setupCategoryEventListeners();
                this.initializeCategoryDragAndDrop();
                this.logDev && this.logDev('Categories module initialized');
            },

            /**
             * Настройка на всички event listeners за категории
             */
            setupCategoryEventListeners: function() {
                this.setupCategoryFilterModal();
                this.setupCategoryModal();
                this.setupCategoryDropdowns();
            },

            /**
             * Настройка на филтър модала за категории
             */
            setupCategoryFilterModal: function() {
                const filterBtn = document.getElementById('filter-btn');
                const filterModal = document.getElementById('filter-modal');
                const closeFilter = document.getElementById('close-filter');
                const filterForm = document.getElementById('filter-form');
                const resetFilter = document.getElementById('reset-filter');

                if (filterBtn && filterModal) {
                    filterBtn.addEventListener('click', () => {
                        this.openCategoryModal(filterModal);
                    });
                }

                if (closeFilter && filterModal) {
                    closeFilter.addEventListener('click', () => {
                        this.closeCategoryModal(filterModal);
                    });
                }

                if (filterModal) {
                    filterModal.addEventListener('click', (e) => {
                        if (e.target === filterModal) {
                            this.closeCategoryModal(filterModal);
                        }
                    });
                }

                if (filterForm) {
                    filterForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCategoryFilterSubmit(filterForm);
                        if (filterModal) {
                            this.closeCategoryModal(filterModal);
                        }
                    });
                }

                if (resetFilter && filterForm) {
                    resetFilter.addEventListener('click', () => {
                        filterForm.reset();
                        this.logDev && this.logDev('Filter form reset');
                    });
                }
            },

            /**
             * Настройка на категория модала
             */
            setupCategoryModal: function() {
                const addCategoryBtn = document.getElementById('add-category-btn');
                const categoryModal = document.getElementById('category-modal');
                const closeCategoryModal = document.getElementById('close-category-modal');
                const cancelCategory = document.getElementById('cancel-category');
                const categoryForm = document.getElementById('category-form');
                const categoryModalTitle = document.getElementById('category-modal-title');
                const editButtons = document.querySelectorAll('.ri-edit-line');

                if (addCategoryBtn && categoryModal) {
                    addCategoryBtn.addEventListener('click', () => {
                        if (categoryModalTitle) {
                            categoryModalTitle.textContent = 'Добавяне на категория';
                        }
                        this.openCategoryModal(categoryModal);
                    });
                }

                if (closeCategoryModal && categoryModal) {
                    closeCategoryModal.addEventListener('click', () => {
                        this.closeCategoryModal(categoryModal);
                    });
                }

                if (cancelCategory && categoryModal) {
                    cancelCategory.addEventListener('click', () => {
                        this.closeCategoryModal(categoryModal);
                    });
                }

                if (categoryModal) {
                    categoryModal.addEventListener('click', (e) => {
                        if (e.target === categoryModal) {
                            this.closeCategoryModal(categoryModal);
                        }
                    });
                }

                if (categoryForm) {
                    categoryForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCategorySubmit(categoryForm);
                        if (categoryModal) {
                            this.closeCategoryModal(categoryModal);
                        }
                    });
                }

                // Setup edit buttons
                editButtons.forEach(btn => {
                    const editLink = btn.closest('a');
                    if (editLink) {
                        editLink.addEventListener('click', (e) => {
                            e.preventDefault();
                            if (categoryModalTitle) {
                                categoryModalTitle.textContent = 'Редактиране на категория';
                            }
                            if (categoryModal) {
                                this.openCategoryModal(categoryModal);
                            }
                        });
                    }
                });
            },

            /**
             * Настройка на dropdown менютата за категории
             */
            setupCategoryDropdowns: function() {
                const dropdownButtons = document.querySelectorAll('[data-dropdown-toggle]');
                
                dropdownButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const targetId = button.getAttribute('data-dropdown-toggle');
                        const dropdown = document.getElementById(targetId);
                        
                        if (dropdown) {
                            // Затваряне на други dropdown менюта
                            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                                if (menu !== dropdown) {
                                    menu.classList.add('hidden');
                                }
                            });
                            
                            // Toggle на текущото dropdown меню
                            dropdown.classList.toggle('hidden');
                        }
                    });
                });

                // Затваряне на dropdown менютата при клик извън тях
                document.addEventListener('click', () => {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                });
            },

            /**
             * Инициализация на drag & drop функционалност за категории
             */
            initializeCategoryDragAndDrop: function() {
                const categoryRows = document.querySelectorAll('.category-row');
                
                if (categoryRows.length > 0) {
                    this.setupCategoryDragAndDrop();
                }
            },

            /**
             * Настройка на drag & drop event handlers за категории
             */
            setupCategoryDragAndDrop: function() {
                const categoryRows = document.querySelectorAll('.category-row');
                
                categoryRows.forEach(row => {
                    // Правим реда draggable
                    row.draggable = true;
                    row.classList.add('drag-ready');
                    
                    // Добавяме event listeners
                    row.addEventListener('dragstart', (e) => this.handleCategoryDragStart(e));
                    row.addEventListener('dragend', (e) => this.handleCategoryDragEnd(e));
                    row.addEventListener('dragover', (e) => this.handleCategoryDragOver(e));
                    row.addEventListener('dragenter', (e) => this.handleCategoryDragEnter(e));
                    row.addEventListener('dragleave', (e) => this.handleCategoryDragLeave(e));
                    row.addEventListener('drop', (e) => this.handleCategoryDrop(e));
                });
            },

            /**
             * Обработка на започване на drag операция
             */
            handleCategoryDragStart: function(e) {
                this.categories.draggedElement = e.target.closest('.category-row');
                this.categories.draggedData = {
                    id: this.getCategoryId(this.categories.draggedElement),
                    parentId: this.getCategoryParentId(this.categories.draggedElement),
                    level: this.getCategoryLevel(this.categories.draggedElement)
                };

                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', e.target.outerHTML);
                
                this.logDev && this.logDev('Drag started for category:', this.categories.draggedData);
            },

            /**
             * Обработка на завършване на drag операция
             */
            handleCategoryDragEnd: function(e) {
                e.target.classList.remove('dragging');
                this.hideAllCategoryDropIndicators();
                
                // Почистване на drop zone класове
                document.querySelectorAll('.category-row').forEach(row => {
                    row.classList.remove('drop-zone-active', 'drop-zone-hover');
                });
                
                this.logDev && this.logDev('Drag ended');
            },

            /**
             * Обработка на drag over събития
             */
            handleCategoryDragOver: function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                
                const targetRow = e.target.closest('.category-row');
                if (targetRow && this.canCategoryDropOn(targetRow)) {
                    const dropPosition = this.getCategoryDropPosition(e, targetRow);
                    this.showCategoryDropIndicator(targetRow, dropPosition);
                }
            },

            /**
             * Обработка на drag enter събития
             */
            handleCategoryDragEnter: function(e) {
                e.preventDefault();
                const targetRow = e.target.closest('.category-row');
                if (targetRow && this.canCategoryDropOn(targetRow)) {
                    targetRow.classList.add('drop-zone-active');
                }
            },

            /**
             * Обработка на drag leave събития
             */
            handleCategoryDragLeave: function(e) {
                const targetRow = e.target.closest('.category-row');
                if (targetRow) {
                    targetRow.classList.remove('drop-zone-active', 'drop-zone-hover');
                }
            },

            /**
             * Обработка на drop операции
             */
            handleCategoryDrop: function(e) {
                e.preventDefault();

                const targetRow = e.target.closest('.category-row');
                if (!targetRow || !this.canCategoryDropOn(targetRow)) {
                    return;
                }

                const dropPosition = this.getCategoryDropPosition(e, targetRow);
                this.performCategoryMove(targetRow, dropPosition);
            },

            /**
             * Проверка дали може да се drop върху елемента
             */
            canCategoryDropOn: function(targetRow) {
                if (!this.categories.draggedElement || !targetRow) {
                    return false;
                }

                // Не може да drop върху себе си
                if (targetRow === this.categories.draggedElement) {
                    return false;
                }

                // Не може да drop върху свои наследници
                if (this.isCategoryDescendant(targetRow, this.categories.draggedElement)) {
                    return false;
                }

                return true;
            },

            /**
             * Проверка дали targetRow е наследник на parentRow
             */
            isCategoryDescendant: function(targetRow, parentRow) {
                const parentId = this.getCategoryId(parentRow);
                const targetParentId = this.getCategoryParentId(targetRow);

                if (targetParentId === parentId) {
                    return true;
                }

                // Рекурсивна проверка нагоре по йерархията
                const targetParentRow = document.querySelector(`[data-category-id="${targetParentId}"]`);
                if (targetParentRow && targetParentRow !== targetRow) {
                    return this.isCategoryDescendant(targetParentRow, parentRow);
                }

                return false;
            },

            /**
             * Определяне на позицията за drop (before/after/inside)
             */
            getCategoryDropPosition: function(e, targetRow) {
                const rect = targetRow.getBoundingClientRect();
                const y = e.clientY - rect.top;
                const height = rect.height;

                if (y < height * 0.25) {
                    return 'before';
                } else if (y > height * 0.75) {
                    return 'after';
                } else {
                    return 'inside';
                }
            },

            /**
             * Показване на drop индикатори
             */
            showCategoryDropIndicator: function(targetRow, position) {
                this.hideAllCategoryDropIndicators();

                const indicator = document.createElement('div');
                indicator.className = `drop-indicator drop-indicator-${position}`;
                indicator.id = 'category-drop-indicator';

                if (position === 'before') {
                    targetRow.parentNode.insertBefore(indicator, targetRow);
                } else if (position === 'after') {
                    targetRow.parentNode.insertBefore(indicator, targetRow.nextSibling);
                } else if (position === 'inside') {
                    indicator.className += ' drop-indicator-inside';
                    targetRow.appendChild(indicator);
                }
            },

            /**
             * Скриване на всички drop индикатори
             */
            hideAllCategoryDropIndicators: function() {
                const indicators = document.querySelectorAll('#category-drop-indicator');
                indicators.forEach(indicator => indicator.remove());
            },

            /**
             * Изпълнение на преместването на категория
             */
            performCategoryMove: function(targetRow, dropPosition) {
                const targetId = this.getCategoryId(targetRow);
                const targetParentId = this.getCategoryParentId(targetRow);

                let newParentId = 0;
                let newSortOrder = 0;

                if (dropPosition === 'inside') {
                    newParentId = targetId;
                    newSortOrder = 1;
                } else {
                    newParentId = targetParentId;
                    const targetSortOrder = parseInt(targetRow.dataset.sortOrder || 0);
                    newSortOrder = dropPosition === 'before' ? targetSortOrder : targetSortOrder + 1;
                }

                this.sendCategoryMoveRequest({
                    categoryId: this.categories.draggedData.id,
                    newParentId: newParentId,
                    newSortOrder: newSortOrder,
                    dropPosition: dropPosition,
                    targetId: targetId
                });
            },

            /**
             * Изпращане на AJAX заявка за преместване
             */
            sendCategoryMoveRequest: function(moveData) {
                const url = 'index.php?route=catalog/category/move&user_token=' + (this.config.userToken || '');

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(moveData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.handleCategoryMoveSuccess(data);
                    } else {
                        this.handleCategoryMoveError(data);
                    }
                })
                .catch(error => {
                    this.handleCategoryMoveError({ error: error.message });
                });
            },

            /**
             * Обработка на успешно преместване
             */
            handleCategoryMoveSuccess: function(data) {
                this.logDev && this.logDev('Category moved successfully:', data);

                // Презареждане на страницата за актуализиране на данните
                if (data.reload !== false) {
                    window.location.reload();
                }
            },

            /**
             * Обработка на грешки при преместване
             */
            handleCategoryMoveError: function(data) {
                console.error('Category move failed:', data);

                const message = data.error || 'Възникна грешка при преместването на категорията.';
                alert(message);
            },

            /**
             * Получаване на category ID от елемента
             */
            getCategoryId: function(element) {
                return element ? parseInt(element.dataset.categoryId || 0) : 0;
            },

            /**
             * Получаване на parent ID от елемента
             */
            getCategoryParentId: function(element) {
                return element ? parseInt(element.dataset.parentId || 0) : 0;
            },

            /**
             * Получаване на нивото на категорията
             */
            getCategoryLevel: function(element) {
                return element ? parseInt(element.dataset.level || 0) : 0;
            },

            /**
             * Обработка на филтър форма
             */
            handleCategoryFilterSubmit: function(form) {
                const formData = new FormData(form);
                const params = new URLSearchParams();

                for (let [key, value] of formData.entries()) {
                    if (value.trim() !== '') {
                        params.append(key, value);
                    }
                }

                // Добавяне на user_token
                if (this.config.userToken) {
                    params.append('user_token', this.config.userToken);
                }

                const url = form.action + '?' + params.toString();
                window.location.href = url;
            },

            /**
             * Обработка на категория форма
             */
            handleCategorySubmit: function(form) {
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert(data.error || 'Възникна грешка при запазването.');
                    }
                })
                .catch(error => {
                    console.error('Form submission error:', error);
                    alert('Възникна грешка при изпращането на формата.');
                });
            },

            /**
             * Отваряне на модал за категории
             */
            openCategoryModal: function(modal) {
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                }
            },

            /**
             * Затваряне на модал за категории
             */
            closeCategoryModal: function(modal) {
                if (modal) {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    document.body.style.overflow = '';
                }
            }
        });

        console.log('[Categories] BackendModule extended successfully');
    } else {
        console.error('[Categories] BackendModule not available for extension');
    }

    console.log('[Categories] Module loaded');
})();
