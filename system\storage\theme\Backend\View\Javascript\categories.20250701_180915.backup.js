/**
 * Categories Management JavaScript Module
 * Управление на категории - JavaScript модул
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

class Categories extends BackendModule {
    constructor() {
        super();
        this.draggedElement = null;
        this.draggedData = null;
        this.dropZones = [];
        this.init();
    }

    /**
     * Инициализация на модула
     */
    init() {
        this.setupEventListeners();
        this.initializeDragAndDrop();
        this.setupDropdowns();
        this.logDev('Categories module initialized');
    }

    /**
     * Настройка на event listeners
     */
    setupEventListeners() {
        // Filter modal functionality
        this.setupFilterModal();
        
        // Category modal functionality  
        this.setupCategoryModal();
        
        // Sidebar toggle
        this.setupSidebarToggle();
        
        // Close dropdowns when clicking outside
        this.setupOutsideClickHandlers();
    }

    /**
     * Настройка на филтър модала
     */
    setupFilterModal() {
        const filterBtn = document.getElementById('filter-btn');
        const filterModal = document.getElementById('filter-modal');
        const closeFilter = document.getElementById('close-filter');
        const filterForm = document.getElementById('filter-form');
        const resetFilter = document.getElementById('reset-filter');

        if (!this.validateElements([filterBtn, filterModal, closeFilter, filterForm, resetFilter])) {
            return;
        }

        filterBtn.addEventListener('click', () => this.openModal(filterModal));
        closeFilter.addEventListener('click', () => this.closeModal(filterModal));
        filterModal.addEventListener('click', (e) => {
            if (e.target === filterModal) {
                this.closeModal(filterModal);
            }
        });

        filterForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFilterSubmit(filterForm);
            this.closeModal(filterModal);
        });

        resetFilter.addEventListener('click', () => {
            filterForm.reset();
            this.logDev('Filter form reset');
        });
    }

    /**
     * Настройка на категория модала
     */
    setupCategoryModal() {
        const addCategoryBtn = document.getElementById('add-category-btn');
        const categoryModal = document.getElementById('category-modal');
        const closeCategoryModal = document.getElementById('close-category-modal');
        const cancelCategory = document.getElementById('cancel-category');
        const categoryForm = document.getElementById('category-form');
        const categoryModalTitle = document.getElementById('category-modal-title');
        const editButtons = document.querySelectorAll('.ri-edit-line');

        if (!categoryModal) return;

        if (addCategoryBtn) {
            addCategoryBtn.addEventListener('click', () => {
                if (categoryModalTitle) {
                    categoryModalTitle.textContent = 'Добавяне на категория';
                }
                this.openModal(categoryModal);
            });
        }

        if (closeCategoryModal) {
            closeCategoryModal.addEventListener('click', () => this.closeModal(categoryModal));
        }

        if (cancelCategory) {
            cancelCategory.addEventListener('click', () => this.closeModal(categoryModal));
        }

        categoryModal.addEventListener('click', (e) => {
            if (e.target === categoryModal) {
                this.closeModal(categoryModal);
            }
        });

        if (categoryForm) {
            categoryForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCategorySubmit(categoryForm);
                this.closeModal(categoryModal);
            });
        }

        // Setup edit buttons
        editButtons.forEach(btn => {
            const editLink = btn.closest('a');
            if (editLink) {
                editLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (categoryModalTitle) {
                        categoryModalTitle.textContent = 'Редактиране на категория';
                    }
                    this.openModal(categoryModal);
                });
            }
        });
    }

    /**
     * Настройка на sidebar toggle
     */
    setupSidebarToggle() {
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const mobileMenu = document.getElementById('mobile-menu');

        if (!this.validateElements([toggleSidebar, sidebar])) {
            return;
        }

        toggleSidebar.addEventListener('click', () => {
            sidebar.classList.toggle('w-64');
            sidebar.classList.toggle('w-20');
            
            const sidebarItems = document.querySelectorAll('.sidebar-item span');
            const sidebarLogo = document.getElementById('sidebar-logo');
            
            sidebarItems.forEach(item => item.classList.toggle('hidden'));
            if (sidebarLogo) sidebarLogo.classList.toggle('hidden');
            
            const icon = toggleSidebar.querySelector('i');
            if (icon) {
                if (icon.classList.contains('ri-menu-fold-line')) {
                    icon.classList.remove('ri-menu-fold-line');
                    icon.classList.add('ri-menu-unfold-line');
                } else {
                    icon.classList.remove('ri-menu-unfold-line');
                    icon.classList.add('ri-menu-fold-line');
                }
            }
        });

        if (mobileMenu) {
            mobileMenu.addEventListener('click', () => {
                sidebar.classList.toggle('-translate-x-full');
            });
        }
    }

    /**
     * Настройка на dropdown менютата
     */
    setupDropdowns() {
        this.setupDropdown('status-dropdown-btn', 'status-dropdown');
        this.setupDropdown('sort-dropdown-btn', 'sort-dropdown');
        this.setupDropdown('per-page-dropdown-btn', 'per-page-dropdown');
    }

    /**
     * Настройка на конкретно dropdown меню
     */
    setupDropdown(btnId, dropdownId) {
        const btn = document.getElementById(btnId);
        const dropdown = document.getElementById(dropdownId);

        if (!this.validateElements([btn, dropdown])) {
            return;
        }

        btn.addEventListener('click', () => {
            dropdown.classList.toggle('hidden');
        });

        const items = dropdown.querySelectorAll('li');
        items.forEach(item => {
            item.addEventListener('click', () => {
                const span = btn.querySelector('span');
                if (span) {
                    span.textContent = item.textContent;
                }
                dropdown.classList.add('hidden');
                this.logDev(`Dropdown ${btnId} selection: ${item.textContent}`);
            });
        });
    }

    /**
     * Настройка на outside click handlers
     */
    setupOutsideClickHandlers() {
        document.addEventListener('click', (event) => {
            this.handleOutsideClick('status-dropdown-btn', 'status-dropdown', event);
            this.handleOutsideClick('sort-dropdown-btn', 'sort-dropdown', event);
            this.handleOutsideClick('per-page-dropdown-btn', 'per-page-dropdown', event);
        });
    }

    /**
     * Обработка на outside click за dropdown
     */
    handleOutsideClick(btnId, dropdownId, event) {
        const btn = document.getElementById(btnId);
        const dropdown = document.getElementById(dropdownId);

        if (btn && dropdown && 
            !btn.contains(event.target) && 
            !dropdown.contains(event.target)) {
            dropdown.classList.add('hidden');
        }
    }

    /**
     * Инициализация на drag and drop функционалност
     */
    initializeDragAndDrop() {
        const categoryItems = document.querySelectorAll('.category-item');
        
        categoryItems.forEach(item => {
            this.setupDragAndDrop(item);
        });

        this.logDev('Drag and drop initialized for categories');
    }

    /**
     * Настройка на drag and drop за конкретен елемент
     */
    setupDragAndDrop(categoryItem) {
        const dragHandle = categoryItem.querySelector('.drag-handle');
        
        if (!dragHandle) return;

        // Make the category item draggable
        categoryItem.draggable = true;
        categoryItem.setAttribute('data-category-id', this.getCategoryId(categoryItem));
        categoryItem.setAttribute('data-parent-id', this.getParentId(categoryItem));
        categoryItem.setAttribute('data-level', this.getCategoryLevel(categoryItem));

        // Drag events
        categoryItem.addEventListener('dragstart', (e) => this.handleDragStart(e, categoryItem));
        categoryItem.addEventListener('dragend', (e) => this.handleDragEnd(e, categoryItem));
        
        // Drop events
        categoryItem.addEventListener('dragover', (e) => this.handleDragOver(e, categoryItem));
        categoryItem.addEventListener('drop', (e) => this.handleDrop(e, categoryItem));
        categoryItem.addEventListener('dragenter', (e) => this.handleDragEnter(e, categoryItem));
        categoryItem.addEventListener('dragleave', (e) => this.handleDragLeave(e, categoryItem));

        // Visual feedback for drag handle
        dragHandle.addEventListener('mouseenter', () => {
            dragHandle.style.cursor = 'grab';
            categoryItem.classList.add('drag-ready');
        });

        dragHandle.addEventListener('mouseleave', () => {
            dragHandle.style.cursor = 'default';
            categoryItem.classList.remove('drag-ready');
        });
    }

    /**
     * Обработка на dragstart събитие
     */
    handleDragStart(e, categoryItem) {
        this.draggedElement = categoryItem;
        this.draggedData = {
            id: categoryItem.getAttribute('data-category-id'),
            parentId: categoryItem.getAttribute('data-parent-id'),
            level: parseInt(categoryItem.getAttribute('data-level'))
        };

        categoryItem.classList.add('dragging');
        categoryItem.style.opacity = '0.5';

        // Set drag effect
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', categoryItem.outerHTML);

        this.logDev('Drag started for category:', this.draggedData);
    }

    /**
     * Обработка на dragend събитие
     */
    handleDragEnd(e, categoryItem) {
        categoryItem.classList.remove('dragging');
        categoryItem.style.opacity = '1';

        // Remove all drop indicators
        document.querySelectorAll('.drop-indicator').forEach(indicator => {
            indicator.remove();
        });

        // Remove drop zone classes
        document.querySelectorAll('.drop-zone-active').forEach(zone => {
            zone.classList.remove('drop-zone-active');
        });

        this.draggedElement = null;
        this.draggedData = null;

        this.logDev('Drag ended');
    }

    /**
     * Обработка на dragover събитие
     */
    handleDragOver(e, categoryItem) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        if (this.canDropOn(categoryItem)) {
            categoryItem.classList.add('drop-zone-active');
            this.showDropIndicator(categoryItem, e);
        }
    }

    /**
     * Обработка на dragenter събитие
     */
    handleDragEnter(e, categoryItem) {
        e.preventDefault();

        if (this.canDropOn(categoryItem)) {
            categoryItem.classList.add('drop-zone-hover');
        }
    }

    /**
     * Обработка на dragleave събитие
     */
    handleDragLeave(e, categoryItem) {
        categoryItem.classList.remove('drop-zone-hover');

        // Remove drop indicator if mouse left the item
        if (!categoryItem.contains(e.relatedTarget)) {
            categoryItem.classList.remove('drop-zone-active');
            this.hideDropIndicator(categoryItem);
        }
    }

    /**
     * Обработка на drop събитие
     */
    handleDrop(e, categoryItem) {
        e.preventDefault();

        if (!this.canDropOn(categoryItem)) {
            this.logDev('Drop not allowed on this target');
            return;
        }

        const dropPosition = this.getDropPosition(categoryItem, e);
        const targetData = {
            id: categoryItem.getAttribute('data-category-id'),
            parentId: categoryItem.getAttribute('data-parent-id'),
            level: parseInt(categoryItem.getAttribute('data-level'))
        };

        this.performCategoryMove(this.draggedData, targetData, dropPosition);

        categoryItem.classList.remove('drop-zone-active', 'drop-zone-hover');
        this.hideDropIndicator(categoryItem);
    }

    /**
     * Проверка дали може да се drop върху елемента
     */
    canDropOn(targetItem) {
        if (!this.draggedElement || !this.draggedData) {
            return false;
        }

        // Не може да drop върху себе си
        if (targetItem === this.draggedElement) {
            return false;
        }

        const targetId = targetItem.getAttribute('data-category-id');
        const targetLevel = parseInt(targetItem.getAttribute('data-level'));

        // Не може да drop върху свои подкатегории
        if (this.isDescendant(targetId, this.draggedData.id)) {
            return false;
        }

        return true;
    }

    /**
     * Проверка дали targetId е наследник на parentId
     */
    isDescendant(targetId, parentId) {
        // Тази логика трябва да се имплементира според структурата на данните
        // За сега връщаме false, но може да се разшири
        return false;
    }

    /**
     * Определяне на позицията за drop
     */
    getDropPosition(categoryItem, e) {
        const rect = categoryItem.getBoundingClientRect();
        const y = e.clientY - rect.top;
        const height = rect.height;

        if (y < height * 0.25) {
            return 'before';
        } else if (y > height * 0.75) {
            return 'after';
        } else {
            return 'inside';
        }
    }

    /**
     * Показване на drop индикатор
     */
    showDropIndicator(categoryItem, e) {
        this.hideDropIndicator(categoryItem);

        const position = this.getDropPosition(categoryItem, e);
        const indicator = document.createElement('div');
        indicator.className = 'drop-indicator';

        if (position === 'before') {
            indicator.className += ' drop-indicator-before';
            categoryItem.parentNode.insertBefore(indicator, categoryItem);
        } else if (position === 'after') {
            indicator.className += ' drop-indicator-after';
            categoryItem.parentNode.insertBefore(indicator, categoryItem.nextSibling);
        } else {
            indicator.className += ' drop-indicator-inside';
            categoryItem.appendChild(indicator);
        }
    }

    /**
     * Скриване на drop индикатор
     */
    hideDropIndicator(categoryItem) {
        const indicators = categoryItem.parentNode.querySelectorAll('.drop-indicator');
        indicators.forEach(indicator => indicator.remove());
    }

    /**
     * Изпълнение на преместването на категория
     */
    performCategoryMove(draggedData, targetData, position) {
        const moveData = {
            category_id: draggedData.id,
            target_id: targetData.id,
            position: position,
            old_parent_id: draggedData.parentId,
            new_parent_id: this.calculateNewParentId(targetData, position)
        };

        this.logDev('Performing category move:', moveData);

        // AJAX заявка към контролера
        this.sendCategoryMoveRequest(moveData);
    }

    /**
     * Изчисляване на новия parent_id
     */
    calculateNewParentId(targetData, position) {
        if (position === 'inside') {
            return targetData.id;
        } else {
            return targetData.parentId;
        }
    }

    /**
     * Изпращане на AJAX заявка за преместване
     */
    sendCategoryMoveRequest(moveData) {
        const url = this.getAjaxUrl('catalog/category/move');

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(moveData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.handleMoveSuccess(data);
            } else {
                this.handleMoveError(data);
            }
        })
        .catch(error => {
            this.logDev('Move request failed:', error);
            this.showNotification('Грешка при преместването на категорията', 'error');
        });
    }

    /**
     * Обработка на успешно преместване
     */
    handleMoveSuccess(data) {
        this.logDev('Category moved successfully:', data);
        this.showNotification('Категорията беше преместена успешно', 'success');

        // Презареждане на страницата или обновяване на DOM
        if (data.reload) {
            window.location.reload();
        } else {
            this.updateCategoryOrder(data);
        }
    }

    /**
     * Обработка на грешка при преместване
     */
    handleMoveError(data) {
        this.logDev('Category move failed:', data);
        this.showNotification(data.error || 'Грешка при преместването на категорията', 'error');
    }

    /**
     * Обновяване на реда на категориите в DOM
     */
    updateCategoryOrder(data) {
        // Тази функция може да се имплементира за динамично обновяване
        // без презареждане на страницата
        if (data.categories) {
            // Обновяване на DOM структурата
            this.logDev('Updating category order in DOM');
        }
    }

    /**
     * Получаване на category ID от елемента
     */
    getCategoryId(categoryItem) {
        // Извличане на ID от URL или data атрибут
        const editLink = categoryItem.querySelector('a[href*="edit"]');
        if (editLink) {
            const href = editLink.getAttribute('href');
            const match = href.match(/category_id=(\d+)/);
            return match ? match[1] : null;
        }
        return null;
    }

    /**
     * Получаване на parent ID от елемента
     */
    getParentId(categoryItem) {
        // Определяне на parent ID според нивото на категорията
        const level = this.getCategoryLevel(categoryItem);
        if (level === 0) {
            return 0; // Главна категория
        }

        // Търсене на родителската категория
        let currentElement = categoryItem.previousElementSibling;
        while (currentElement) {
            const currentLevel = this.getCategoryLevel(currentElement);
            if (currentLevel < level) {
                return this.getCategoryId(currentElement);
            }
            currentElement = currentElement.previousElementSibling;
        }

        return 0;
    }

    /**
     * Получаване на нивото на категорията
     */
    getCategoryLevel(categoryItem) {
        // Определяне на нивото според CSS класовете за margin
        const classes = categoryItem.className;
        const match = classes.match(/ml-(\d+)/);
        if (match) {
            return Math.floor(parseInt(match[1]) / 8); // ml-8 = level 1, ml-16 = level 2, etc.
        }
        return 0;
    }

    /**
     * Обработка на филтър форма
     */
    handleFilterSubmit(form) {
        const formData = new FormData(form);
        const params = new URLSearchParams();

        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                params.append(key, value);
            }
        }

        // Пренасочване с филтър параметри
        const currentUrl = new URL(window.location);
        params.forEach((value, key) => {
            currentUrl.searchParams.set(key, value);
        });

        window.location.href = currentUrl.toString();
        this.logDev('Filter applied:', Object.fromEntries(params));
    }

    /**
     * Обработка на категория форма
     */
    handleCategorySubmit(form) {
        // Тази функция може да се разшири за AJAX запазване
        this.logDev('Category form submitted');
        form.submit();
    }

    /**
     * Отваряне на модал
     */
    openModal(modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        this.logDev('Modal opened');
    }

    /**
     * Затваряне на модал
     */
    closeModal(modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
        this.logDev('Modal closed');
    }

    /**
     * Валидация на елементи
     */
    validateElements(elements) {
        return elements.every(element => element !== null);
    }
}

// Инициализация на модула при зареждане на страницата
document.addEventListener('DOMContentLoaded', function() {
    if (typeof BackendModule !== 'undefined') {
        window.categoriesModule = new Categories();
    } else {
        console.error('BackendModule base class not found. Please ensure it is loaded before Categories module.');
    }
});
